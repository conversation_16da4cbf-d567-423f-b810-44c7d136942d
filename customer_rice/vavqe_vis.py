﻿import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
from tqdm import tqdm
from vqvae_ver2 import ProteinVQVAE  # 导入之前定义的模型
from vqvae_ver2 import ProteinDataset  # 导入之前定义的数据集

def analyze_similarity(model, dataloader, device='cuda'):
    """分析codebook向量和encoder输出的相似度"""
    model.eval()
    
    # 获取codebook向量
    codebook = model.vq.embeddings.weight.detach()  # [num_embeddings, embedding_dim]
    codebook_norm = F.normalize(codebook, p=2, dim=1)
    
    # 计算codebook内部的余弦相似度
    codebook_sim = torch.mm(codebook_norm, codebook_norm.t())  # [num_embeddings, num_embeddings]
    codebook_sim_np = codebook_sim.cpu().numpy()
    
    # 存储encoder输出的相似度结果
    encoder_sims = []
    max_sims = []
    
    with torch.no_grad():
        for batch in tqdm(dataloader, desc="Analyzing similarity"):
            coords = batch['coords'].to(device)
            types = batch['types'].to(device)
            mask = batch['mask'].to(device).bool()
            
            # 获取encoder输出（量化前）
            h = model.encode(coords, types, mask)  # [B, N, E]
            h_norm = F.normalize(h, p=2, dim=-1)
            
            # 计算encoder输出与codebook的相似度
            sim = torch.einsum('bne,ke->bnk', h_norm, codebook_norm)  # [B, N, K]
            
            # 只考虑有效原子
            valid_mask = mask.unsqueeze(-1).expand_as(sim)
            valid_sims = sim[valid_mask].view(-1, codebook.size(0))
            
            # 记录每个向量的最大相似度
            max_sim, _ = valid_sims.max(dim=1)
            max_sims.append(max_sim.cpu())
            
            # 记录所有相似度
            encoder_sims.append(valid_sims.cpu())
    
    # 合并结果
    encoder_sims = torch.cat(encoder_sims, dim=0)  # [total_atoms, num_embeddings]
    max_sims = torch.cat(max_sims, dim=0)          # [total_atoms]
    
    return codebook_sim_np, encoder_sims.numpy(), max_sims.numpy()

def plot_similarity_distribution(codebook_sim, encoder_sims, max_sims):
    """绘制相似度分布图"""
    plt.figure(figsize=(15, 10))
    
    # 1. Codebook内部相似度分布（排除对角线）
    plt.subplot(2, 2, 1)
    triu_indices = np.triu_indices_from(codebook_sim, k=1)
    codebook_flat = codebook_sim[triu_indices]
    plt.hist(codebook_flat, bins=50, alpha=0.7, color='blue')
    plt.title('Codebook Internal Cosine Similarity')
    plt.xlabel('Similarity')
    plt.ylabel('Frequency')
    plt.grid(True)
    
    # 2. Encoder输出与codebook的平均相似度分布
    plt.subplot(2, 2, 2)
    avg_sim = encoder_sims.mean(axis=1)
    plt.hist(avg_sim, bins=50, alpha=0.7, color='green')
    plt.title('Average Similarity to Codebook')
    plt.xlabel('Average Similarity')
    plt.ylabel('Frequency')
    plt.grid(True)
    
    # 3. Encoder输出与最接近codebook向量的相似度分布
    plt.subplot(2, 2, 3)
    plt.hist(max_sims, bins=50, alpha=0.7, color='red')
    plt.title('Max Similarity to Codebook')
    plt.xlabel('Max Similarity')
    plt.ylabel('Frequency')
    plt.grid(True)
    
    # 4. Codebook相似度矩阵可视化
    plt.subplot(2, 2, 4)
    plt.imshow(codebook_sim, cmap='viridis', vmin=-1, vmax=1)
    plt.colorbar(label='Cosine Similarity')
    plt.title('Codebook Similarity Matrix')
    
    plt.tight_layout()
    plt.savefig('codebook_similarity_analysis.png')
    plt.show()

if __name__ == "__main__":
    # 配置参数
    PDB_DIR = "/path/to/your/pdbs"
    PDB_DIR = "/home/<USER>/code/MAPE-PPI/data/raw_data/Arabidopsis_pdb"  # 替换为你的PDB文件目录
    MODEL_PATH = "protein_vqvae.pth"
    MAX_ATOMS = 30000
    BATCH_SIZE = 8
    NUM_SAMPLES = 100  # 用于分析的样本数量
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
    
    # 加载数据集
    dataset = ProteinDataset(PDB_DIR, max_atoms=MAX_ATOMS)
    # 创建子集用于分析
    indices = torch.randperm(len(dataset))[:NUM_SAMPLES]
    subset = torch.utils.data.Subset(dataset, indices)
    dataloader = DataLoader(subset, batch_size=BATCH_SIZE, shuffle=False)
    
    # 初始化并加载模型
    model = ProteinVQVAE(
        num_atom_types=len(dataset.ATOM_TYPE_MAP),
        embedding_dim=256,
        num_embeddings=1024,
        commitment=0.75
    ).to(DEVICE)
    model.load_state_dict(torch.load(MODEL_PATH, map_location=DEVICE))
    
    # 分析相似度
    codebook_sim, encoder_sims, max_sims = analyze_similarity(
        model, dataloader, DEVICE
    )
    
    # 保存结果
    np.savez('similarity_results.npz',
             codebook_sim=codebook_sim,
             encoder_sims=encoder_sims,
             max_sims=max_sims)
    
    # 绘制分布图
    plot_similarity_distribution(codebook_sim, encoder_sims, max_sims)
    
    # 打印统计信息
    print("\n===== Similarity Analysis Results =====")
    print(f"Codebook internal similarity (mean): {np.mean(codebook_sim):.4f}")
    print(f"Encoder output avg similarity to codebook: {np.mean(encoder_sims):.4f}")
    print(f"Encoder output max similarity to codebook: {np.mean(max_sims):.4f}")
    print(f"Percentage of encoder outputs with similarity > 0.9: {np.mean(max_sims > 0.9) * 100:.2f}%")