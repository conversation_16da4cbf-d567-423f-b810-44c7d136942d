#!/usr/bin/env python3
"""
简单测试脚本，验证 SQ-VAE 模型的基本功能
"""

import torch
import torch.nn as nn
import numpy as np

# 导入我们的模型
from vqvae_ver3 import StochasticVectorQuantizer, ProteinSQVAE

def test_stochastic_quantizer():
    """测试随机量化器"""
    print("Testing StochasticVectorQuantizer...")
    
    # 创建测试数据
    batch_size, seq_len, embed_dim = 2, 10, 64
    num_embeddings = 128
    
    # 测试 Gaussian 量化
    print("  Testing Gaussian quantization...")
    sq_gaussian = StochasticVectorQuantizer(
        num_embeddings=num_embeddings,
        embedding_dim=embed_dim,
        quantization_type='gaussian'
    )
    
    inputs = torch.randn(batch_size, seq_len, embed_dim)
    mask = torch.ones(batch_size, seq_len, dtype=torch.bool)
    
    # 训练模式
    quant_train, loss_train, perp_train = sq_gaussian(inputs, mask, training=True)
    print(f"    Training - Loss: {loss_train:.4f}, Perplexity: {perp_train:.2f}")
    
    # 推理模式
    quant_eval, loss_eval, perp_eval = sq_gaussian(inputs, mask, training=False)
    print(f"    Inference - Loss: {loss_eval:.4f}, Perplexity: {perp_eval:.2f}")
    
    # 测试 vMF 量化
    print("  Testing vMF quantization...")
    sq_vmf = StochasticVectorQuantizer(
        num_embeddings=num_embeddings,
        embedding_dim=embed_dim,
        quantization_type='vmf'
    )
    
    quant_train, loss_train, perp_train = sq_vmf(inputs, mask, training=True)
    print(f"    Training - Loss: {loss_train:.4f}, Perplexity: {perp_train:.2f}")
    
    quant_eval, loss_eval, perp_eval = sq_vmf(inputs, mask, training=False)
    print(f"    Inference - Loss: {loss_eval:.4f}, Perplexity: {perp_eval:.2f}")
    
    print("  ✓ StochasticVectorQuantizer tests passed!")

def test_protein_sqvae():
    """测试蛋白质 SQ-VAE 模型"""
    print("\nTesting ProteinSQVAE...")
    
    # 创建测试数据
    batch_size, max_atoms = 2, 100
    num_atom_types = 43  # 根据 ATOM_TYPE_MAP
    
    coords = torch.randn(batch_size, max_atoms, 3)
    types = torch.randint(0, num_atom_types, (batch_size, max_atoms))
    mask = torch.ones(batch_size, max_atoms, dtype=torch.bool)
    # 模拟一些填充
    mask[:, 80:] = False
    
    # 测试 Gaussian SQ-VAE
    print("  Testing Gaussian SQ-VAE...")
    model_gaussian = ProteinSQVAE(
        num_atom_types=num_atom_types,
        embedding_dim=128,
        num_embeddings=256,
        quantization_type='gaussian'
    )
    
    recon_coords, logits, sq_loss, perplexity = model_gaussian(
        coords, types, mask, training=True
    )
    
    print(f"    Input shape: {coords.shape}")
    print(f"    Reconstructed coords shape: {recon_coords.shape}")
    print(f"    Logits shape: {logits.shape}")
    print(f"    SQ Loss: {sq_loss:.4f}")
    print(f"    Perplexity: {perplexity:.2f}")
    
    # 测试温度设置
    model_gaussian.set_temperature(0.5)
    recon_coords2, logits2, sq_loss2, perplexity2 = model_gaussian(
        coords, types, mask, training=True
    )
    print(f"    After temperature change - SQ Loss: {sq_loss2:.4f}, Perplexity: {perplexity2:.2f}")
    
    # 测试 vMF SQ-VAE
    print("  Testing vMF SQ-VAE...")
    model_vmf = ProteinSQVAE(
        num_atom_types=num_atom_types,
        embedding_dim=128,
        num_embeddings=256,
        quantization_type='vmf'
    )
    
    recon_coords, logits, sq_loss, perplexity = model_vmf(
        coords, types, mask, training=True
    )
    
    print(f"    SQ Loss: {sq_loss:.4f}")
    print(f"    Perplexity: {perplexity:.2f}")
    
    print("  ✓ ProteinSQVAE tests passed!")

def test_temperature_schedule():
    """测试温度调度"""
    print("\nTesting temperature schedule...")
    
    from vqvae_ver3 import temperature_schedule
    
    epochs = 10
    temperatures = []
    for epoch in range(epochs):
        temp = temperature_schedule(epoch, epochs, temp_init=1.0, temp_final=0.1)
        temperatures.append(temp)
        print(f"  Epoch {epoch+1}: Temperature = {temp:.3f}")
    
    # 验证温度是递减的
    assert all(temperatures[i] >= temperatures[i+1] for i in range(len(temperatures)-1))
    assert temperatures[0] == 1.0
    assert abs(temperatures[-1] - 0.1) < 1e-6
    
    print("  ✓ Temperature schedule tests passed!")

if __name__ == "__main__":
    print("Running SQ-VAE tests...\n")
    
    try:
        test_stochastic_quantizer()
        test_protein_sqvae()
        test_temperature_schedule()
        
        print("\n🎉 All tests passed! The SQ-VAE implementation is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
