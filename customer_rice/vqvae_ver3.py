import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torch.distributions import Categorical
import numpy as np
import os
from Bio.PDB import PDBParser
from Bio.PDB.PDBExceptions import PDBConstructionWarning
import warnings
from tqdm import tqdm
import matplotlib.pyplot as plt

# 忽略PDB解析警告
warnings.filterwarnings("ignore", category=PDBConstructionWarning)


class ProteinDataset(Dataset):
    ATOM_TYPE_MAP = {
        'C': 0, 'CA': 1, 'CB': 2, 'CD': 3, 'CD1': 4, 'CD2': 5, 'CE': 6, 'CE1': 7,
        'CE2': 8, 'CE3': 9, 'CG': 10, 'CG1': 11, 'CG2': 12, 'CH2': 13, 'CZ': 14,
        'CZ2': 15, 'CZ3': 16, 'N': 17, 'ND1': 18, 'ND2': 19, 'NE': 20, 'NE1': 21,
        'NE2': 22, 'NH1': 23, 'NH2': 24, 'NZ': 25, 'O': 26, 'OD1': 27, 'OD2': 28,
        'OE1': 29, 'OE2': 30, 'OG': 31, 'OG1': 32, 'OH': 33, 'SD': 34, 'SG': 35,
        'H': 36, 'HA': 37, 'HB': 38, 'HD': 39, 'HE': 40, 'HH': 41, 'HZ': 42
    }

    def __init__(self, pdb_dir, max_atoms=1000):
        self.pdb_files = [
            os.path.join(pdb_dir, f)
            for f in os.listdir(pdb_dir) if f.endswith('.pdb')
        ]
        self.max_atoms = max_atoms
        self.parser = PDBParser(QUIET=True)
        print(f"Found {len(self.pdb_files)} PDB files.")

    def __len__(self):
        return len(self.pdb_files)

    def __getitem__(self, idx):
        path = self.pdb_files[idx]
        try:
            structure = self.parser.get_structure("prot", path)
            atoms = [atom for model in structure
                           for chain in model
                           for residue in chain
                           for atom in residue]
            coords = []
            types = []
            for atom in atoms:
                coords.append(atom.get_coord())
                t = atom.get_name().strip()
                types.append(self.ATOM_TYPE_MAP.get(t, len(self.ATOM_TYPE_MAP)))
            coords = np.array(coords, dtype=np.float32)
            types  = np.array(types, dtype=np.int64)

            # 截断或填充
            N = len(coords)
            if N > self.max_atoms:
                idxs = np.random.choice(N, self.max_atoms, replace=False)
                coords, types = coords[idxs], types[idxs]
                mask = np.ones(self.max_atoms, dtype=bool)
            else:
                pad = self.max_atoms - N
                coords = np.pad(coords, ((0,pad),(0,0)), 'constant', constant_values=0)
                types  = np.pad(types, (0,pad), 'constant', constant_values=len(self.ATOM_TYPE_MAP))
                mask = np.concatenate([np.ones(N, dtype=bool), np.zeros(pad, dtype=bool)])

            # 归一化
            mu = coords.mean(axis=0)
            st = coords.std(axis=0) + 1e-8
            coords = (coords - mu) / st

            return {
                'coords': torch.tensor(coords),
                'types':  torch.tensor(types),
                'mask':   torch.tensor(mask)
            }
        except Exception as e:
            # 出错时返回全零
            return {
                'coords': torch.zeros(self.max_atoms,3),
                'types':  torch.zeros(self.max_atoms,dtype=torch.long),
                'mask':   torch.zeros(self.max_atoms,dtype=torch.bool)
            }


def sample_gumbel(shape, eps=1e-10, device='cuda'):
    """Sample from Gumbel distribution"""
    U = torch.rand(shape, device=device)
    return -torch.log(-torch.log(U + eps) + eps)


def gumbel_softmax_sample(logits, temperature):
    """Sample from Gumbel-Softmax distribution"""
    g = sample_gumbel(logits.size(), device=logits.device)
    y = logits + g
    return F.softmax(y / temperature, dim=-1)


def calc_distance(z_continuous, codebook):
    """Calculate squared Euclidean distance between continuous latents and codebook"""
    z_flat = z_continuous.view(-1, codebook.size(1))
    distances = (torch.sum(z_flat**2, dim=1, keepdim=True) 
                + torch.sum(codebook**2, dim=1)
                - 2 * torch.matmul(z_flat, codebook.t()))
    return distances


class StochasticVectorQuantizer(nn.Module):
    """Stochastic Vector Quantizer for SQ-VAE"""
    def __init__(self, num_embeddings, embedding_dim, temperature_init=1.0, 
                 quantization_type='gaussian', commitment_cost=0.25):
        super().__init__()
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
        self.temperature = temperature_init
        self.quantization_type = quantization_type
        self.commitment_cost = commitment_cost
        
        # Codebook
        self.codebook = nn.Parameter(torch.randn(num_embeddings, embedding_dim))
        nn.init.uniform_(self.codebook, -1.0, 1.0)
        
        # Learnable variance parameter for Gaussian quantization
        if quantization_type == 'gaussian':
            self.log_var_q = nn.Parameter(torch.tensor(0.0))
        elif quantization_type == 'vmf':
            self.log_kappa = nn.Parameter(torch.tensor(1.0))
    
    def set_temperature(self, temperature):
        """Set temperature for annealing"""
        self.temperature = temperature
    
    def forward(self, inputs, mask=None, training=True):
        """
        Forward pass of stochastic quantization
        Args:
            inputs: [B, N, E] continuous latent representations
            mask: [B, N] mask for valid positions
            training: whether in training mode
        """
        if self.quantization_type == 'gaussian':
            return self._gaussian_quantize(inputs, mask, training)
        elif self.quantization_type == 'vmf':
            return self._vmf_quantize(inputs, mask, training)
        else:
            raise ValueError(f"Unknown quantization type: {self.quantization_type}")
    
    def _gaussian_quantize(self, inputs, mask, training):
        """Gaussian stochastic quantization"""
        B, N, E = inputs.shape

        # Calculate precision (inverse variance)
        precision_q = 1.0 / torch.clamp(self.log_var_q.exp(), min=1e-10)

        # Calculate logits (negative weighted distances)
        distances = calc_distance(inputs, self.codebook)  # [B*N, K]
        logits = -0.5 * precision_q * distances  # [B*N, K]

        # Calculate probabilities
        probabilities = F.softmax(logits, dim=-1)
        log_probabilities = F.log_softmax(logits, dim=-1)

        if training:
            # Stochastic quantization using Gumbel-Softmax
            encodings = gumbel_softmax_sample(logits, self.temperature)
            z_quantized = torch.matmul(encodings, self.codebook)
            avg_probs = torch.mean(probabilities.detach(), dim=0)
        else:
            # Deterministic quantization for inference
            indices = torch.argmax(logits, dim=-1)
            encodings = F.one_hot(indices, num_classes=self.num_embeddings).float()
            z_quantized = torch.matmul(encodings, self.codebook)
            avg_probs = torch.mean(probabilities, dim=0)

        z_quantized = z_quantized.view(B, N, E)

        # Calculate losses
        # KL divergence for discrete distribution
        kld_discrete = torch.sum(probabilities * log_probabilities, dim=0).mean()

        # Continuous loss (commitment loss)
        if mask is not None:
            valid_mask = mask.view(-1)
            continuous_loss = F.mse_loss(
                z_quantized.view(-1, E)[valid_mask],
                inputs.view(-1, E)[valid_mask].detach()
            )
        else:
            continuous_loss = F.mse_loss(z_quantized, inputs.detach())

        total_loss = kld_discrete + self.commitment_cost * continuous_loss

        # Calculate perplexity
        perplexity = torch.exp(-torch.sum(avg_probs * torch.log(avg_probs + 1e-7)))

        # Straight-through estimator
        z_quantized_st = inputs + (z_quantized - inputs).detach()

        return z_quantized_st, total_loss, perplexity
    
    def _vmf_quantize(self, inputs, mask, training):
        """von Mises-Fisher stochastic quantization"""
        B, N, E = inputs.shape

        # Normalize inputs and codebook
        inputs_norm = F.normalize(inputs, p=2.0, dim=-1)
        codebook_norm = F.normalize(self.codebook, p=2.0, dim=-1)

        # Calculate concentration parameter
        kappa = self.log_kappa.exp() + 1.0

        # Calculate logits (weighted cosine similarities)
        inputs_flat = inputs_norm.view(-1, self.embedding_dim)
        logits = kappa * torch.matmul(inputs_flat, codebook_norm.t())

        # Calculate probabilities
        probabilities = F.softmax(logits, dim=-1)
        log_probabilities = F.log_softmax(logits, dim=-1)

        if training:
            # Stochastic quantization using Gumbel-Softmax
            encodings = gumbel_softmax_sample(logits, self.temperature)
            z_quantized = torch.matmul(encodings, codebook_norm)
            avg_probs = torch.mean(probabilities.detach(), dim=0)
        else:
            # Deterministic quantization for inference
            indices = torch.argmax(logits, dim=-1)
            encodings = F.one_hot(indices, num_classes=self.num_embeddings).float()
            z_quantized = torch.matmul(encodings, codebook_norm)
            avg_probs = torch.mean(probabilities, dim=0)

        z_quantized = z_quantized.view(B, N, self.embedding_dim)
        
        # Calculate losses
        # KL divergence for discrete distribution
        kld_discrete = torch.sum(probabilities * log_probabilities, dim=0).mean()
        
        # Continuous loss for vMF
        if mask is not None:
            valid_mask = mask.view(-1)
            continuous_loss = torch.sum(
                inputs_norm.view(-1, self.embedding_dim)[valid_mask] * 
                (inputs_norm.view(-1, self.embedding_dim)[valid_mask] - z_quantized.view(-1, self.embedding_dim)[valid_mask]) * kappa
            ).mean()
        else:
            continuous_loss = torch.sum(inputs_norm * (inputs_norm - z_quantized) * kappa, dim=-1).mean()
        
        total_loss = kld_discrete + continuous_loss
        
        # Calculate perplexity
        perplexity = torch.exp(-torch.sum(avg_probs * torch.log(avg_probs + 1e-7)))
        
        # Straight-through estimator
        z_quantized_st = inputs + (z_quantized - inputs).detach()
        
        return z_quantized_st, total_loss, perplexity


class ProteinSQVAE(nn.Module):
    """Protein Stochastically Quantized Variational Autoencoder"""
    def __init__(self, num_atom_types, embedding_dim=128, num_embeddings=512,
                 temperature_init=1.0, quantization_type='gaussian', commitment=0.25):
        super().__init__()
        self.num_atom_types = num_atom_types
        self.embedding_dim = embedding_dim
        self.quantization_type = quantization_type

        # 编码器部分
        self.atom_emb = nn.Embedding(num_atom_types+1, embedding_dim)
        self.coord_net = nn.Sequential(
            nn.Linear(3, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        self.fuse = nn.Sequential(
            nn.Linear(2*embedding_dim, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embedding_dim, nhead=8, dim_feedforward=512, batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=4)
        self.proj = nn.Sequential(
            nn.Linear(embedding_dim, embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(embedding_dim)
        )

        # 随机量化器
        self.sq = StochasticVectorQuantizer(
            num_embeddings, embedding_dim, temperature_init, quantization_type, commitment
        )

        # 解码器：同时输出坐标和原子类型
        self.decoder = nn.Sequential(
            nn.Linear(embedding_dim, 2 * embedding_dim),
            nn.ReLU(),
            nn.LayerNorm(2 * embedding_dim)
        )
        self.coord_head = nn.Linear(2 * embedding_dim, 3)  # 坐标输出层
        self.type_head = nn.Linear(2 * embedding_dim, num_atom_types+1)  # 原子类型输出层

        # 重构损失的可学习方差参数
        self.log_var_recon = nn.Parameter(torch.tensor(np.log(0.1)))

    def forward(self, coords, types, mask=None, training=True):
        # 编码过程
        t = self.atom_emb(types)           # [B,N,E]
        c = self.coord_net(coords)         # [B,N,E]
        x = self.fuse(torch.cat([t, c], dim=-1))

        # Transformer编码
        pad_mask = ~mask if mask is not None else None
        h = self.transformer(x, src_key_padding_mask=pad_mask)
        h = self.proj(h)

        # 随机量化
        quant, sq_loss, perplexity = self.sq(h, mask, training)

        # 解码过程
        dec_out = self.decoder(quant)
        recon_coords = self.coord_head(dec_out)  # [B,N,3]
        logits = self.type_head(dec_out)        # [B,N,num_atom_types+1]

        return recon_coords, logits, sq_loss, perplexity

    def set_temperature(self, temperature):
        """设置量化温度（用于自退火）"""
        self.sq.set_temperature(temperature)


def temperature_schedule(epoch, total_epochs, temp_init=1.0, temp_final=0.1):
    """温度退火调度：从高温度逐渐降到低温度"""
    progress = epoch / total_epochs
    # 指数衰减
    temperature = temp_init * (temp_final / temp_init) ** progress
    return max(temperature, temp_final)


def train_sqvae(pdb_dir, epochs=50, batch_size=4, max_atoms=1000, device='cuda',
                quantization_type='gaussian', use_arelbo=True):
    """训练 SQ-VAE 模型"""
    dataset = ProteinDataset(pdb_dir, max_atoms)
    dl = DataLoader(dataset, batch_size=batch_size, shuffle=True, num_workers=4)

    model = ProteinSQVAE(
        num_atom_types=len(dataset.ATOM_TYPE_MAP),
        embedding_dim=256,
        num_embeddings=1024,
        temperature_init=1.0,
        quantization_type=quantization_type,
        commitment=0.75
    ).to(device)

    opt = optim.Adam(model.parameters(), lr=1e-3)
    losses = []
    temperatures = []

    for ep in range(epochs):
        # 温度退火
        current_temp = temperature_schedule(ep, epochs, temp_init=1.0, temp_final=0.1)
        model.set_temperature(current_temp)
        temperatures.append(current_temp)

        model.train()
        total_loss = 0
        coord_loss_total = 0
        type_loss_total = 0
        sq_loss_total = 0
        perplexity_total = 0

        for batch in tqdm(dl, desc=f"Epoch {ep+1}/{epochs} (T={current_temp:.3f})"):
            coords = batch['coords'].to(device)
            types  = batch['types'].to(device)
            mask   = batch['mask'].to(device)

            # 前向传播
            recon_coords, logits, sq_loss, perplexity = model(coords, types, mask, training=True)

            # 计算重构损失（只对真实原子计算）
            valid_mask = mask

            # 1. 坐标重构损失
            coord_loss = F.mse_loss(recon_coords[valid_mask], coords[valid_mask])

            # 2. 原子类型重构损失（交叉熵）
            type_loss = F.cross_entropy(
                logits[valid_mask],
                types[valid_mask],
                ignore_index=len(dataset.ATOM_TYPE_MAP)  # 忽略填充的原子类型
            )

            # 3. 总重构损失
            if use_arelbo:
                # 使用 ARELBO (Annealed Reconstruction Error Lower Bound)
                # 防止后验坍塌的技术
                mse_total = coord_loss + type_loss
                recon_loss = len(dataset.ATOM_TYPE_MAP) * torch.log(mse_total) / 2
            else:
                # 传统的重构损失
                recon_loss = (coord_loss + type_loss) / (2 * model.log_var_recon.exp()) + \
                           len(dataset.ATOM_TYPE_MAP) * model.log_var_recon / 2

            # 总损失 = 重构损失 + SQ损失
            loss = recon_loss + sq_loss

            opt.zero_grad()
            loss.backward()
            opt.step()

            # 记录各项损失
            coord_loss_total += coord_loss.item()
            type_loss_total += type_loss.item()
            sq_loss_total += sq_loss.item()
            perplexity_total += perplexity.item()
            total_loss += loss.item()

        avg = total_loss / len(dl)
        avg_perplexity = perplexity_total / len(dl)
        losses.append(avg)

        print(f"[{ep+1}/{epochs}] Total loss: {avg:.4f} | "
              f"Coord: {coord_loss_total/len(dl):.4f} | "
              f"Type: {type_loss_total/len(dl):.4f} | "
              f"SQ: {sq_loss_total/len(dl):.4f} | "
              f"Perplexity: {avg_perplexity:.2f} | "
              f"Temperature: {current_temp:.3f}")

    # 绘制并保存损失曲线和温度曲线
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))

    ax1.plot(losses)
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.set_title('SQ-VAE Training Loss')

    ax2.plot(temperatures)
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Temperature')
    ax2.set_title('Temperature Annealing')

    plt.tight_layout()
    plt.savefig('sqvae_training.png')
    plt.close()

    return model


def encode_proteins(model, pdb_dir, max_atoms=1000, device='cuda'):
    """使用训练好的 SQ-VAE 模型编码蛋白质"""
    dataset = ProteinDataset(pdb_dir, max_atoms)
    dl = DataLoader(dataset, batch_size=1, shuffle=False)
    codes_list = []
    perplexities = []

    model.eval()
    with torch.no_grad():
        for batch in tqdm(dl, desc="Encoding"):
            coords = batch['coords'].to(device)
            types  = batch['types'].to(device)
            mask   = batch['mask'].to(device)

            # 使用确定性量化进行编码
            _, _, _, perplexity = model(coords, types, mask, training=False)

            # 获取量化后的编码
            # 这里我们需要直接访问量化器来获取离散编码
            t = model.atom_emb(types)
            c = model.coord_net(coords)
            x = model.fuse(torch.cat([t, c], dim=-1))
            pad_mask = ~mask if mask is not None else None
            h = model.transformer(x, src_key_padding_mask=pad_mask)
            h = model.proj(h)

            # 获取离散编码索引
            if model.quantization_type == 'gaussian':
                precision_q = 1.0 / torch.clamp(model.sq.log_var_q.exp(), min=1e-10)
                distances = calc_distance(h, model.sq.codebook)
                logits = -0.5 * precision_q * distances
            else:  # vmf
                h_norm = F.normalize(h, p=2.0, dim=-1)
                codebook_norm = F.normalize(model.sq.codebook, p=2.0, dim=-1)
                kappa = model.sq.log_kappa.exp() + 1.0
                h_flat = h_norm.view(-1, model.embedding_dim)
                logits = kappa * torch.matmul(h_flat, codebook_norm.t())

            indices = torch.argmax(logits, dim=-1).view(coords.shape[0], coords.shape[1])
            valid_codes = indices[mask].cpu().numpy()
            codes_list.append(valid_codes)
            perplexities.append(perplexity.item())

    return codes_list, perplexities


def compare_quantization_methods(pdb_dir, epochs=30, batch_size=8, max_atoms=30000, device='cuda'):
    """比较不同量化方法的性能"""
    print("Comparing Gaussian vs vMF quantization methods...")

    results = {}

    for quant_type in ['gaussian', 'vmf']:
        print(f"\n=== Training with {quant_type.upper()} quantization ===")

        model = train_sqvae(
            pdb_dir=pdb_dir,
            epochs=epochs,
            batch_size=batch_size,
            max_atoms=max_atoms,
            device=device,
            quantization_type=quant_type,
            use_arelbo=True
        )

        # 保存模型
        torch.save(model.state_dict(), f"protein_sqvae_{quant_type}.pth")

        # 编码蛋白质
        codes, perplexities = encode_proteins(model, pdb_dir, max_atoms=max_atoms, device=device)

        results[quant_type] = {
            'model': model,
            'codes': codes,
            'perplexities': perplexities,
            'avg_perplexity': np.mean(perplexities)
        }

        print(f"{quant_type.upper()} Results:")
        print(f"  Average perplexity: {results[quant_type]['avg_perplexity']:.2f}")
        print(f"  Codebook utilization examples:")
        for i, c in enumerate(codes[:5]):
            unique_codes = np.unique(c)
            print(f"    Protein {i+1}: {len(unique_codes)} unique codes")

    return results


if __name__ == "__main__":
    # PDB_DIR = "/path/to/your/pdbs"
    # PDB_DIR = "/home/<USER>/code/MAPE-PPI/data/raw_data/all_pdbs"  # 替换为你的PDB文件目录
    PDB_DIR = "/home/<USER>/code/MAPE-PPI/data/raw_data/Arabidopsis_pdb"  # 替换为你的PDB文件目录

    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"

    print(f"Using device: {DEVICE}")
    print("Training Protein SQ-VAE with self-annealing stochastic quantization...")

    # 比较不同的量化方法
    results = compare_quantization_methods(
        pdb_dir=PDB_DIR,
        epochs=30,
        batch_size=8,
        max_atoms=30000,
        device=DEVICE
    )

    # 输出比较结果
    print("\n=== Final Comparison ===")
    for quant_type, result in results.items():
        print(f"{quant_type.upper()}: Avg Perplexity = {result['avg_perplexity']:.2f}")

    print("\nTraining completed! Models saved as 'protein_sqvae_gaussian.pth' and 'protein_sqvae_vmf.pth'")
    print("Training plots saved as 'sqvae_training.png'")
