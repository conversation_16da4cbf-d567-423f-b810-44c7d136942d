﻿import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import os
from tqdm import tqdm
import argparse
from vqvae_ver2 import ProteinVQVAE  # 导入之前定义的模型
from vqvae_ver2 import ProteinDataset  # 导入之前定义的数据集

class ModelAnalyzer:
    def __init__(self, model_path, device='cuda'):
        self.device = device
        self.model = self.load_model(model_path)
        self.codebook = self.extract_codebook()
        self.usage_stats = self.calculate_codebook_usage()
    
    def load_model(self, model_path):
        """加载预训练模型"""
        # 创建模型实例（参数暂时为占位符）
        model = ProteinVQVAE(
            num_atom_types=43,  # 默认值，会在加载权重后更新
            embedding_dim=256,
            num_embeddings=1024,
            commitment=0.75
        ).to(self.device)
        
        # 加载模型权重
        state_dict = torch.load(model_path, map_location=self.device)
        model.load_state_dict(state_dict)
        model.eval()
        print(f"Model loaded from {model_path}")
        
        # 提取实际参数
        self.embedding_dim = model.vq.embeddings.weight.shape[1]
        self.num_embeddings = model.vq.embeddings.weight.shape[0]
        
        return model
    
    def extract_codebook(self):
        """提取码本向量"""
        codebook = self.model.vq.embeddings.weight.detach()
        return codebook
    
    def calculate_codebook_usage(self):
        """计算码本使用统计"""
        # 计算每个码本向量的L2范数
        norms = torch.norm(self.codebook, dim=1).cpu().numpy()
        
        # 计算码本内部相似度
        codebook_norm = F.normalize(self.codebook, p=2, dim=1)
        sim_matrix = torch.mm(codebook_norm, codebook_norm.t()).cpu().numpy()
        
        # 提取非对角线元素
        triu_indices = np.triu_indices_from(sim_matrix, k=1)
        sim_values = sim_matrix[triu_indices]
        
        return {
            'norms': norms,
            'sim_matrix': sim_matrix,
            'sim_values': sim_values,
            'mean_sim': np.mean(sim_values),
            'std_sim': np.std(sim_values),
            'min_sim': np.min(sim_values),
            'max_sim': np.max(sim_values)
        }
    
    def analyze_encoder_outputs(self, dataset, num_samples=10):
        """分析encoder输出"""
        dataloader = torch.utils.data.DataLoader(
            dataset, batch_size=1, shuffle=True
        )
        
        all_encoder_outputs = []
        all_max_sims = []
        
        with torch.no_grad():
            for i, batch in enumerate(tqdm(dataloader, desc="Analyzing encoder outputs")):
                if i >= num_samples:
                    break
                
                coords = batch['coords'].to(self.device)
                types = batch['types'].to(self.device)
                mask = batch['mask'].to(self.device).bool()
                
                # 获取encoder输出（量化前）
                t = self.model.atom_emb(types)
                c = self.model.coord_net(coords)
                x = self.model.fuse(torch.cat([t, c], dim=-1))
                pad_mask = ~mask
                h = self.model.transformer(x, src_key_padding_mask=pad_mask)
                h = self.model.proj(h)
                
                # 只考虑有效原子
                valid_h = h[mask].squeeze(0)
                
                # 计算每个输出与码本的相似度
                h_norm = F.normalize(valid_h, p=2, dim=1)
                cb_norm = F.normalize(self.codebook, p=2, dim=1)
                sims = h_norm @ cb_norm.t()
                
                # 记录最大相似度
                max_sims, _ = torch.max(sims, dim=1)
                all_max_sims.append(max_sims.cpu())
                all_encoder_outputs.append(valid_h.cpu())
        
        # 合并结果
        if all_encoder_outputs:
            encoder_outputs = torch.cat(all_encoder_outputs, dim=0).numpy()
            max_sims = torch.cat(all_max_sims, dim=0).numpy()
        else:
            encoder_outputs = np.array([])
            max_sims = np.array([])
        
        return {
            'encoder_outputs': encoder_outputs,
            'max_sims': max_sims,
            'mean_max_sim': np.mean(max_sims) if len(max_sims) > 0 else 0,
            'median_max_sim': np.median(max_sims) if len(max_sims) > 0 else 0
        }
    
    def visualize_analysis(self, usage_stats, encoder_stats):
        """可视化分析结果"""
        plt.figure(figsize=(18, 12))
        
        # 1. 码本向量范数分布
        plt.subplot(2, 3, 1)
        plt.hist(usage_stats['norms'], bins=50, alpha=0.7, color='skyblue')
        plt.title('Codebook Vector Norms')
        plt.xlabel('L2 Norm')
        plt.ylabel('Frequency')
        plt.grid(True, alpha=0.3)
        
        # 2. 码本内部相似度分布
        plt.subplot(2, 3, 2)
        plt.hist(usage_stats['sim_values'], bins=50, alpha=0.7, color='salmon')
        plt.title('Codebook Internal Similarity')
        plt.xlabel('Cosine Similarity')
        plt.ylabel('Frequency')
        plt.grid(True, alpha=0.3)
        
        # 3. 码本相似度矩阵
        plt.subplot(2, 3, 3)
        plt.imshow(usage_stats['sim_matrix'], cmap='viridis', vmin=-1, vmax=1)
        plt.colorbar(label='Cosine Similarity')
        plt.title('Codebook Similarity Matrix')
        
        # 4. Encoder输出最大相似度分布
        if len(encoder_stats['max_sims']) > 0:
            plt.subplot(2, 3, 4)
            plt.hist(encoder_stats['max_sims'], bins=50, alpha=0.7, color='mediumseagreen')
            plt.title('Max Similarity to Codebook')
            plt.xlabel('Cosine Similarity')
            plt.ylabel('Frequency')
            plt.grid(True, alpha=0.3)
            
            # 添加统计信息
            stats_text = f"Mean: {encoder_stats['mean_max_sim']:.3f}\nMedian: {encoder_stats['median_max_sim']:.3f}"
            plt.text(0.05, 0.95, stats_text, transform=plt.gca().transAxes, 
                     verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.5))
        
        # 5. Encoder输出PCA可视化（示例）
        if len(encoder_stats['encoder_outputs']) > 0:
            plt.subplot(2, 3, 5)
            # 简单示例：只显示前两个维度
            outputs = encoder_stats['encoder_outputs']
            plt.scatter(outputs[:, 0], outputs[:, 1], alpha=0.3, s=5, color='purple')
            plt.title('Encoder Outputs (First Two Dimensions)')
            plt.xlabel('Dimension 1')
            plt.ylabel('Dimension 2')
            plt.grid(True, alpha=0.3)
        
        # 6. 码本使用热力图（示例）
        plt.subplot(2, 3, 6)
        # 这里可以添加码本使用频率的热力图
        plt.text(0.5, 0.5, 'Codebook Usage Heatmap\n(Placeholder)', 
                 ha='center', va='center', fontsize=12)
        plt.axis('off')
        
        plt.tight_layout()
        plt.savefig('vqvae_analysis_report.png', dpi=150)
        plt.close()
        
        # 创建文本报告
        report = self.generate_text_report(usage_stats, encoder_stats)
        with open('vqvae_analysis_report.txt', 'w') as f:
            f.write(report)
        
        print("Analysis report saved to vqvae_analysis_report.png and vqvae_analysis_report.txt")
    
    def generate_text_report(self, usage_stats, encoder_stats):
        """生成文本分析报告"""
        report = "===== VQ-VAE Model Analysis Report =====\n\n"
        
        report += "1. Codebook Statistics:\n"
        report += f"  - Embedding dimension: {self.embedding_dim}\n"
        report += f"  - Number of embeddings: {self.num_embeddings}\n"
        report += f"  - Mean vector norm: {np.mean(usage_stats['norms']):.4f}\n"
        report += f"  - Norm std. dev.: {np.std(usage_stats['norms']):.4f}\n"
        report += f"  - Mean similarity: {usage_stats['mean_sim']:.4f}\n"
        report += f"  - Similarity std. dev.: {usage_stats['std_sim']:.4f}\n"
        report += f"  - Min similarity: {usage_stats['min_sim']:.4f}\n"
        report += f"  - Max similarity: {usage_stats['max_sim']:.4f}\n\n"
        
        report += "2. Encoder-Codebook Relationship:\n"
        if len(encoder_stats['max_sims']) > 0:
            report += f"  - Mean max similarity: {encoder_stats['mean_max_sim']:.4f}\n"
            report += f"  - Median max similarity: {encoder_stats['median_max_sim']:.4f}\n"
            report += f"  - Samples with similarity > 0.9: {np.mean(encoder_stats['max_sims'] > 0.9)*100:.1f}%\n"
            report += f"  - Samples with similarity < 0.7: {np.mean(encoder_stats['max_sims'] < 0.7)*100:.1f}%\n"
        else:
            report += "  No encoder outputs analyzed\n"
        
        report += "\n3. Health Assessment:\n"
        report += self.assess_model_health(usage_stats, encoder_stats)
        
        return report
    
    def assess_model_health(self, usage_stats, encoder_stats):
        """评估模型健康状况"""
        assessment = ""
        
        # 1. 码本多样性评估
        mean_sim = usage_stats['mean_sim']
        if mean_sim > 0.3:
            assessment += "  - Warning: High codebook similarity (>0.3) suggests under-utilization\n"
        elif mean_sim < -0.1:
            assessment += "  - Warning: Low codebook similarity (<-0.1) may indicate training instability\n"
        else:
            assessment += "  - Codebook diversity: Good (similarity around 0)\n"
        
        # 2. 码本向量规范评估
        mean_norm = np.mean(usage_stats['norms'])
        if mean_norm < 0.5:
            assessment += "  - Warning: Low vector norms (<0.5) may indicate weak representations\n"
        elif mean_norm > 1.5:
            assessment += "  - Warning: High vector norms (>1.5) may cause training instability\n"
        else:
            assessment += "  - Vector norms: Optimal (0.5-1.5 range)\n"
        
        # 3. Encoder-码本匹配评估
        if len(encoder_stats['max_sims']) > 0:
            mean_max_sim = encoder_stats['mean_max_sim']
            if mean_max_sim < 0.7:
                assessment += "  - Critical: Low encoder-codebook similarity (<0.7) indicates poor quantization\n"
            elif mean_max_sim < 0.8:
                assessment += "  - Warning: Moderate encoder-codebook similarity (0.7-0.8)\n"
            elif mean_max_sim < 0.9:
                assessment += "  - Good: Solid encoder-codebook similarity (0.8-0.9)\n"
            else:
                assessment += "  - Excellent: High encoder-codebook similarity (>0.9)\n"
        
        # 4. 整体评估
        if "Warning" in assessment or "Critical" in assessment:
            assessment += "\n  Overall: Model may need adjustment (see warnings above)"
        else:
            assessment += "\n  Overall: Model appears healthy"
        
        return assessment

class ProteinDataset(Dataset):
    ATOM_TYPE_MAP = {
        'C': 0, 'CA': 1, 'CB': 2, 'CD': 3, 'CD1': 4, 'CD2': 5, 'CE': 6, 'CE1': 7,
        'CE2': 8, 'CE3': 9, 'CG': 10, 'CG1': 11, 'CG2': 12, 'CH2': 13, 'CZ': 14,
        'CZ2': 15, 'CZ3': 16, 'N': 17, 'ND1': 18, 'ND2': 19, 'NE': 20, 'NE1': 21,
        'NE2': 22, 'NH1': 23, 'NH2': 24, 'NZ': 25, 'O': 26, 'OD1': 27, 'OD2': 28,
        'OE1': 29, 'OE2': 30, 'OG': 31, 'OG1': 32, 'OH': 33, 'SD': 34, 'SG': 35,
        'H': 36, 'HA': 37, 'HB': 38, 'HD': 39, 'HE': 40, 'HH': 41, 'HZ': 42
    }

    def __init__(self, pdb_dir, max_atoms=1000):
        self.pdb_files = [
            os.path.join(pdb_dir, f)
            for f in os.listdir(pdb_dir) if f.endswith('.pdb')
        ][:10]  # 只使用前10个文件用于分析
        self.max_atoms = max_atoms
        self.parser = PDBParser(QUIET=True)
        print(f"Using {len(self.pdb_files)} PDB files for analysis")

    def __len__(self):
        return len(self.pdb_files)

    def __getitem__(self, idx):
        path = self.pdb_files[idx]
        try:
            structure = self.parser.get_structure("prot", path)
            atoms = [atom for model in structure for chain in model for residue in chain for atom in residue]
            coords = []
            types = []
            for atom in atoms:
                coords.append(atom.get_coord())
                t = atom.get_name().strip()
                types.append(self.ATOM_TYPE_MAP.get(t, len(self.ATOM_TYPE_MAP)))
            coords = np.array(coords, dtype=np.float32)
            types = np.array(types, dtype=np.int64)

            N = len(coords)
            if N > self.max_atoms:
                idxs = np.random.choice(N, self.max_atoms, replace=False)
                coords, types = coords[idxs], types[idxs]
                mask = np.ones(self.max_atoms, dtype=bool)
            else:
                pad = self.max_atoms - N
                coords = np.pad(coords, ((0,pad),(0,0)), 'constant', constant_values=0)
                types = np.pad(types, (0,pad), 'constant', constant_values=len(self.ATOM_TYPE_MAP))
                mask = np.concatenate([np.ones(N, dtype=bool), np.zeros(pad, dtype=bool)])

            mu = coords.mean(axis=0)
            st = coords.std(axis=0) + 1e-8
            coords = (coords - mu) / st

            return {
                'coords': torch.tensor(coords),
                'types': torch.tensor(types),
                'mask': torch.tensor(mask)
            }
        except Exception as e:
            return {
                'coords': torch.zeros(self.max_atoms,3),
                'types': torch.zeros(self.max_atoms,dtype=torch.long),
                'mask': torch.zeros(self.max_atoms,dtype=torch.bool)
            }

def main():
    parser = argparse.ArgumentParser(description='Analyze VQ-VAE model internals')
    parser.add_argument('--model_path', type=str, required=True, help='Path to trained model')
    parser.add_argument('--pdb_dir', type=str, required=True, help='Directory with PDB files')
    parser.add_argument('--max_atoms', type=int, default=1000, help='Max atoms per protein')
    parser.add_argument('--device', type=str, default='cuda', choices=['cuda', 'cpu'], help='Device to use')
    args = parser.parse_args()

    device = torch.device(args.device if torch.cuda.is_available() and args.device == 'cuda' else 'cpu')
    print(f"Using device: {device}")

    # 创建分析器
    analyzer = ModelAnalyzer(args.model_path, device)
    
    # 创建数据集
    dataset = ProteinDataset(args.pdb_dir, max_atoms=args.max_atoms)
    
    # 分析encoder输出
    encoder_stats = analyzer.analyze_encoder_outputs(dataset, num_samples=10)
    
    # 可视化分析结果
    analyzer.visualize_analysis(analyzer.usage_stats, encoder_stats)

if __name__ == "__main__":
    main()