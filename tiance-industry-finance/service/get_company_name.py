#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time    : 2025/6/12 下午6:30
# <AUTHOR> <PERSON><PERSON>yuanYi
# @Email   : <EMAIL>
# @File    : get_company_name.py
# @Project : tiance-industry-finance
"""
import json
import asyncio
from configs.mongodb_config import MongodbConfig
from configs.milvus_config import MilvusConfig
from configs.model_config import ModelConfig
from utils.mongodb_util import MongodbUtil
from utils.milvus_util import MilvusUtil
from service.llm_service import Llm_Service
from entity.message_entity import UserMessage, MessageConverter
from utils.log_util import LogUtil
import re


def check_parsed_status(doc: dict):
    """检查文档是否已解析"""
    parse_time = doc.get('time', {}).get('parse_time')
    milvus_collection = doc.get('milvus', {}).get('milvus_collection_name')
    file_title = doc.get('file', {}).get('file_title')

    if parse_time is None and milvus_collection is None:
        return False, None
    elif parse_time is None or milvus_collection is None:
        raise ValueError("文档部分解析，但缺少必要字段")
    return True, milvus_collection, file_title


def _extract_from_text(text: str):
    """从文本中提取公司名称（备用方法）"""
    # 更健壮的公司名称正则表达式
    pattern = r'([\u4e00-\u9fa5（）()a-zA-Z0-9]{2,}?(?:公司|集团|有限公司|有限责任公司|股份有限公司))'
    return list(set(re.findall(pattern, text)))


class CompanyExtractor:
    def __init__(self, mongodb_collection: str, mongodb_id: str):
        self.mongodb_collection = mongodb_collection
        self.mongodb_id = mongodb_id
        self.milvus_util = MilvusUtil()
        MongodbUtil.connect()

        # 初始化大模型服务
        self.llm_service = Llm_Service(model=ModelConfig.MAX_LLM_MODEL_NAME)
        self.model_name = ModelConfig.MAX_LLM_MODEL_NAME

    def get_mongodb_document(self):
        """从MongoDB获取指定文档"""
        doc = MongodbUtil.query_doc_by_id(self.mongodb_collection, self.mongodb_id)
        if not doc:
            raise ValueError(f"在集合 {self.mongodb_collection} 中未找到ID为 {self.mongodb_id} 的文档")
        return doc

    def get_milvus_chunks(self, milvus_collection: str):
        """从Milvus获取相关文本块"""
        # 构建查询条件
        query_condition = f'mongodb_id == "{self.mongodb_id}"'

        # 查询Milvus获取所有相关块
        chunks = self.milvus_util.query_by_scalar(
            collection_name=milvus_collection,
            query_conditions=query_condition,
            output_fields=["chunk_content_son"]
        )

        if not chunks:
            raise ValueError(f"在Milvus集合 {milvus_collection} 中未找到mongodb_id为 {self.mongodb_id} 的文档")

        # 提取并拼接所有文本块内容
        return "\n\n".join([
            chunk['chunk_content_son']
            for chunk in chunks
            if chunk.get('chunk_content_son') is not None
        ])

    async def extract_companies(self, text: str):
        """使用大模型提取公司名称"""
        # 构建系统提示
        system_prompt = (
            "你是一个专业的信息提取助手，需要从用户提供的文本中提取所有公司名称。"
            "请严格按照以下要求执行：\n"
            "1. 只提取明确提到的公司名称，不要推断或创造名称\n"
            "2. 公司名称需完整准确，包含完整公司后缀（如有限公司、集团等）\n"
            "3. 以JSON格式返回结果，格式为：{\"companies\": [\"公司1\", \"公司2\"]}\n"
            "4. 不要包含任何解释性文字或额外信息"
        )

        # 构建用户消息
        user_message = f"请从以下文本中提取所有公司名称：\n\n{text}"

        # 创建消息列表
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_message}
        ]

        # 调用大模型
        response = await self.llm_service.answer_question(
            messages=messages,
            model=self.model_name,
            max_tokens=1024
        )

        # 尝试解析JSON响应
        try:
            # 尝试直接解析JSON
            return json.loads(response)
        except json.JSONDecodeError:
            # 尝试从响应中提取JSON部分
            json_match = re.search(r'\{.*}', response, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group())
                except json.JSONDecodeError:
                    LogUtil.warn(f"无法解析模型返回的JSON: {response}")
            else:
                LogUtil.warn(f"模型返回非标准JSON响应: {response}")

            # 最后尝试使用文本提取
            companies = _extract_from_text(response)
            return {"companies": companies}

    def save_to_json(self, data: dict, filename: str = None):
        """将结果保存为JSON文件"""
        if not filename:
            filename = f"companies_{self.mongodb_collection}_{self.mongodb_id}.json"

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

        return filename
