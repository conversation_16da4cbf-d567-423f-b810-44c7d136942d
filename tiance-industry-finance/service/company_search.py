"""
# @Time         : 2025/06/16 18:05
# <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
# @File         : company_search.py
# @Description  : 根据公司名称从mysql数据库中获取信息和更新数据库
"""

import sys
from pathlib import Path
import re

from sqlalchemy import text
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
from utils.sql_util import SQLUtil
from entity.mysql_entity import CompanyMain, EP_CompanyMain


class CompanySearch:
    def __init__(self):
        SQLUtil.connect()
    def search_company(self, company_name):
        ## 根据公司名称查询公司信息
        ## company_name   公司名称
        ## return         公司信息
        ## 查询CompanyMain中公司信息
        result = self.fulltext_search(company_name)
        if len(result)>0:
            result = self.process_data(result)
            return result
        else:
            ## 查询EP_CompanyMain中公司信息
            result = self.ep_fulltext_search(company_name)
            result = self.process_ep_data(result)
            if len(result)>0:
                
                for company_name_info in result:
                    creditCode = company_name_info['CreditCode']
                    ## 确认CreditCode是否存在
                    credit_result = self.check_creditCode(creditCode)
                    if len(credit_result)>0:
                        ## 更新CompanyMain   是否是这样更新
                        credit_result = SQLUtil.model_to_dict(credit_result[0])
                        self.update_CompanyMain(credit_result,company_name_info)
                    else:
                        ## 插入CompanyMain
                        self.insert_CompanyMain(company_name_info)
                    result = self.fulltext_search(company_name)
                return self.process_data(result)
            else:
                return None
                # ## 调用天眼查接口获取数据
                # tianyan_result = self.tianyan_search(company_name)
                # creditCode = tianyan_result['CreditCode']
                # ## 确认CreditCode是否存在
                # credit_result = self.check_creditCode(creditCode)
                # if len(credit_result)>0:
                #     ## 更新CompanyMain
                #     self.update_CompanyMain(credit_result[0],tianyan_result)
                # else:
                #     ## 插入CompanyMain
                #     self.insert_CompanyMain(tianyan_result)



        
    def fulltext_search(self, company_name):
        ## 全文检索 
        sql_text = text(
            """
            SELECT *,MATCH(ChiName, ChiNameAbbr, PreName, EngName, EngNameAbbr, StockAbbr, BrandName, LargeModelAbbr, OtherAbbr) 
            AGAINST(:search_name IN BOOLEAN MODE) as relevance FROM CompanyMain 
            WHERE MATCH(ChiName, ChiNameAbbr, PreName, EngName, EngNameAbbr, StockAbbr, BrandName, LargeModelAbbr, OtherAbbr) 
            AGAINST(:search_name IN BOOLEAN MODE)
            order by relevance desc
            limit 10;
            """
        )
        search_term = {
            "search_name":company_name + "*" 
        }
        result = SQLUtil.execute_sql(sql_text,search_term)  ##返回结果是数组
        return result

    def ep_fulltext_search(self, company_name):
        ## EP_CompanyMain全文检索 
        sql_text = text(
            """
            SELECT *,MATCH(ChiName) 
            AGAINST(:search_name IN BOOLEAN MODE) as relevance FROM EP_CompanyMain 
            WHERE MATCH(ChiName) 
            AGAINST(:search_name IN BOOLEAN MODE)
            order by relevance desc
            limit 10;
            """
        )
        search_term = {
            "search_name":company_name + "*" 
        }
        result = SQLUtil.execute_sql(sql_text,search_term)  ##返回结果是数组
        return result

    def tianyan_search(self, company_name):
        ## 天眼搜索
        pass

    def update_CompanyMain(self, old_info,new_info):
        ## 更新CompanyMain中的数据
        data = old_info
        used_name = old_info['ChiName']
        data['ChiName'] = new_info['ChiName']
        data['PreName'] = data['PreName']+"," + used_name
        SQLUtil.update_by_id(CompanyMain, old_info["CompanyCode"],data)

    def insert_CompanyMain(self, company_name_info):
        company_data = {}
        exists_id = SQLUtil.get_max_id(CompanyMain,"CompanyCode")
        match = re.match(r"([A-Za-z]+)(\d+)", exists_id).groups()
        count = int(match[1])
        count += 1
        primary_key_model = "C{:09d}"
        company_data["CompanyCode"] = primary_key_model.format(count)
        company_data["ChiName"] = company_name_info["ChiName"]
        company_data["ChiNameAbbr"] = company_name_info["ChiNameAbbr"]
        company_data["PreName"] = "-"
        company_data["EngName"] = company_name_info["EngName"]
        company_data["EngNameAbbr"] = company_name_info["EngNameAbbr"]
        company_data["StockAbbr"] = "-"
        company_data["BrandName"] = "-"
        company_data["LargeModelAbbr"] = "-"
        company_data["OtherAbbr"] = "-"
        company_data["CreditCode"] = company_name_info["CreditCode"]
        SQLUtil.insert_one(CompanyMain,company_data)

        
    ## 确认CreditCode是否存在
    # 检查信用代码是否已存在
    def check_creditCode(self, creditCode):
        # 根据信用代码查询公司主表中的数据，精确匹配，限制返回结果为1条
        credit_result = SQLUtil.query_by_column(model = CompanyMain, column_name = 'CreditCode', column_value = creditCode,exact_match = True, limit = 1)
        # 返回查询结果
        return credit_result

    def process_tianyan_data(self,tianyan_data):
        ## 处理天眼数据
        pass

    def process_data(self,company_info):
        ## 处理数据库中获取的数据，数组形式，
        data_list=[]
        for data in company_info:
            
            data_info = {
                "CompanyCode": data[0],
                "ChiName": data[1],
                "ChiNameAbbr": data[2],
                "PreName": data[3],
                "EngName": data[4],
                "EngNameAbbr": data[5],
                "StockAbbr": data[6],
                "BrandName": data[7],
                "LargeModelAbbr": data[8],
                "OtherAbbr": data[9],
                "CreditCode": data[10]     
            }
            data_list.append(data_info)
        return data_list
    
    def process_ep_data(self,company_info):
        ## 处理数据库中获取的数据，数组形式，
        data_list=[]
        for data in company_info:
            data_info = {
                "ChiName": data[3],
                "ChiNameAbbr": data[4],
                "EngName": data[5],
                "EngNameAbbr": data[6],
                "CreditCode": data[8]     
            }
            
            data_list.append(data_info)
        return data_list
    
    def is_former_name(self, new_name, former_names_str):
        """
        判断公司名称是否在曾用名列表中
        
        :param company_name: 要判断的公司名称
        :param former_names_str: 包含曾用名的字符串，用逗号分隔
        :return: True/False
        """
        if not former_names_str:  # 处理空字符串情况
            return False
    
        # 分割曾用名字符串，并去除每个曾用名两端的空白字符
        former_names = [name.strip() for name in former_names_str.split(",")]
        
        # 检查公司名称是否在曾用名列表中
        return new_name in former_names



if __name__ == '__main__':
    # SQLUtil.connect()
    # result = SQLUtil.get_data_by_id(EP_CompanyMain,'600000000042')
    # dict = SQLUtil.model_to_dict(result)
    # print(dict)
    company_search = CompanySearch()
    # res = company_search.fulltext_search("瑞华泰")
    # res = company_search.check_creditCode("9144030076757494XN")[0]
    res = company_search.search_company("北京兆丰洲商社")
    print(res)