#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/05/20 17:38
# <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
# @File         : news_pageparse_service.py
# @Description  : 资讯数据网页解析
"""

import os
from lxml import html
from PIL import Image
from reportlab.lib.pagesizes import letter
from reportlab.platypus import Paragraph, SimpleDocTemplate, Image as RLImage
from reportlab.lib.units import inch
import urllib.request
import re
import sys
from pathlib import Path

import requests
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
from configs.pdfstyle_config import styles
from utils.chorme_driver_utils import ChromeDriver
from configs.minio_config import MinioConfig
from configs.news_crawl_config import NewsCrawlConfig
from datetime import datetime

class WebPageParser:
    def __init__(self, page_source):
        self.tree = html.fromstring(page_source)
    
    def get_page_info(self, title_xpath, source_path, release_time_xpath,  content_list_xpath, list_type, date_format,url,img_url_header="", distince_xpath="",cookies=""):
        """
        解析网页内容，获取页面信息
        :param title_xpath: 标题的XPath
        :param source_path: 来源/作者的XPath
        :param release_time_xpath: 发布时间的XPath
        :param content_list_xpath: 正文内容的XPath
        :param list_type: 正文内容的类型，如 'li' 、 'p' 、'div'
        :param date_format: 日期格式，用于解析日期
        :param img_url_header: 图片url链接拼接头 ，有些网页有，有些没有
        :param distince_xpath: 地点的XPath,有些网页有，有些没有
        """
        ## 标题
        try:
            title = self.tree.xpath(title_xpath+'/text()')[0]
        except IndexError:
            title = "N/A"
        title = re.sub(r'[^\w\s]|\s', '', title)
        
        ## 来源/作者
        try:
            # source = self.tree.xpath(source_path+'/text()')[0]
            source = self.tree.xpath(source_path)[0]
            source = source.xpath('string(.)')
            source = source.replace(' ', '').replace('\n', '')
        except IndexError:
            source = ""  # 或者用默认值，如 "N/A" 或 None

        ## 时间
        try: 
            # release_time = self.tree.xpath(release_time_xpath+'/text()')[0]
            # if 
            release_time = self.tree.xpath(release_time_xpath)[0]
            release_time = release_time.xpath('string(.)')
  
        except IndexError:
            release_time = ""  # 或者用默认值，如 "N/A" 或 None
        ## 地点
        try:
            distinct = self.tree.xpath(distince_xpath+'/text()')[0]
        except IndexError:
            distinct = None  # 或者用默认值，如 "N/A" 或 None

        try:
            content = self.tree.xpath(content_list_xpath)[0]
            content_list = content.xpath(list_type)
        except IndexError:
            print("content_list_xpath错误")
            return
        
        ## 正文内容
        content_text = ''

        ##  将内容写入pdf文件中
        pdf_file_name = title + '.pdf'
        pdf_file_path = NewsCrawlConfig.LOCAL_PDF_PATH + title + '.pdf'
        if os.path.exists(pdf_file_path):  # 如果文件已经存在，则跳过下载
            print(pdf_file_name + "文件已存在，跳过下载")
        else:
            doc = SimpleDocTemplate(pdf_file_path, pagesize=letter)
            story = []
            story.append(Paragraph(title,styles['titleStyle']))
            # 创建单个文件夹
            folder_name = NewsCrawlConfig.LOCAL_IMAGES_PATH + title + '图片'
            if not os.path.exists(folder_name):  # 检查文件夹是否存在
                os.mkdir(folder_name)  # 如果不存在则创建
            i=0
            for li in content_list:
                content_text = li.xpath('string(.)')
                story.append(Paragraph(content_text, styles['ChineseStyle']))
                img_li = li.xpath('.//img')
                if len(img_li) >0:
                    for img_url_li in img_li: # 获取图片链接
                        if img_url_header =="Xinhua":
                            src = img_url_li.xpath('./@src')[0]
                            parts = url.split('/')  # 按 '/' 分割成列表
                            result = '/'.join(parts[:-1])
                            img_url = result+'/' + src
                            
                        else:
                            if img_url_header =="data-src":
                                img_url_part = img_url_li.xpath('./@data-src')[0]
                            else:
                                if 'src' in img_url_li.attrib:
                                    img_url_part = img_url_li.xpath('./@src')[0]
                                else:
                                    img_url_part =  img_url_li.xpath('./@data-src')[0]
                            if img_url_part.startswith("http"):
                                img_url = img_url_part
                            else:
                                img_url = img_url_header +  img_url_part

                        i +=1
                        filename = folder_name+'/'+'图'+str(i) +'.jpg'
                        if os.path.exists(filename):  # 如果文件已经存在，则跳过下载
                            print("图片"+ filename + "已存在，跳过下载")
                        else:
                            if cookies == "":
                                urllib.request.urlretrieve(img_url, filename)
                            else:
                                self.get_img_request(img_url,filename,cookies)
                            # urllib.request.urlretrieve(img_url, filename) 
                        try:
                            # 检查图片是否存在且有效
                            if os.path.exists(filename):
                                # 获取图片尺寸以保持比例
                                img = Image.open(filename)
                                img_width, img_height = img.size
                                aspect = img_height / float(img_width)
                                
                                # 设置PDF中图片的宽度（例如页面宽度的80%）
                                pdf_img_width = 5 * inch
                                pdf_img_height = pdf_img_width * aspect

                                
                                if pdf_img_height > 636:
                                    pdf_img_height = 636
                                    pdf_img_width = pdf_img_height / aspect

                                
                                # 添加图片到PDF
                                story.append(RLImage(filename, width=pdf_img_width, height=pdf_img_height))
                                story.append(Paragraph("<br/><br/>", styles['Normal']))  # 图片后添加间距
                        except Exception as e:
                            print(f"处理图片失败: {filename}, 错误: {e}")
                            
            doc.build(story)
            print("生成PDF文档成功！")
        

        if date_format == "ZQRB":
            
            parts = source.split()
            release_time = parts[0] + ' ' + parts[1]
            source = ' '.join(parts[2:])
            release_time = datetime.strptime(release_time, "%Y-%m-%d %H:%M")
        elif date_format == "dk":
            year = release_time[:4]
            release_time = year + '-'+ release_time[4:]
            release_time = datetime.strptime(release_time, "%Y-%m/%d")
        else:

            if release_time != "":
                release_time = release_time.strip()
                release_time = datetime.strptime(release_time, date_format)

        data = {
            "title": title,
            "source": source,
            "release_time": release_time,
            "pdf_file_name": pdf_file_name,
            "distinct": distinct,
            "url":url,
        }
        return data


    def get_muti_content(self, content_list_xpath, list_type):
        content = self.tree.xpath(content_list_xpath)  ## "/*"
        for content_list in content:
            content_detail_list = content_list.xpath(list_type)
            for li in content_detail_list:
                pass

    def get_img_request(self, img_url, filename,cookies):
        headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
       

        response = requests.get(img_url, headers=headers, cookies=cookies, stream=True)

        if response.status_code == 200:
            with open(filename, "wb") as f:
                for chunk in response.iter_content(1024):  # 分块写入，避免内存溢出
                    f.write(chunk)
            print("图片下载成功！")
        else:
            print("下载失败，状态码:", response.status_code)

    def get_content_info(self, content_list_xpath, title):
        try:
            content = self.tree.xpath(content_list_xpath)[0]
            content_list = content.xpath('./*')
        except IndexError:
            print("获取正文内容失败")
            return ""
        ## 正文内容
        content_text = ''

        ##  将内容写入pdf文件中
        pdf_file_name = title + '.pdf'
        pdf_file_path = NewsCrawlConfig.LOCAL_PDF_PATH + title + '.pdf'
        if os.path.exists(pdf_file_path):  # 如果文件已经存在，则跳过下载
            print(pdf_file_name + "文件已存在，跳过下载")
        else:
            doc = SimpleDocTemplate(pdf_file_path, pagesize=letter)
            story = []
            story.append(Paragraph(title,styles['titleStyle']))
            # 创建单个文件夹
            folder_name = NewsCrawlConfig.LOCAL_IMAGES_PATH + title + '图片'
            if not os.path.exists(folder_name):  # 检查文件夹是否存在
                os.mkdir(folder_name)  # 如果不存在则创建
            
            i=0
            for li in content_list:
                content_text = li.xpath('string(.)')
                story.append(Paragraph(content_text, styles['ChineseStyle']))
                img_li = li.xpath('.//img')
                if len(img_li) >0:
                    for img_url_li in img_li: # 获取图片链接
                        
                        
                        if 'src' in img_url_li.attrib:
                            img_url_part = img_url_li.xpath('./@src')[0]
                        else:
                            img_url_part =  img_url_li.xpath('./@data-src')[0]
                        if img_url_part.startswith("http"):
                            img_url = img_url_part
                        else:
                            img_url = "https:" +  img_url_part

                        i +=1
                        filename = folder_name+'/'+'图'+str(i) +'.jpg'
                        if os.path.exists(filename):  # 如果文件已经存在，则跳过下载
                            print("图片"+ filename + "已存在，跳过下载")
                        else:
                            
                            urllib.request.urlretrieve(img_url, filename)
                           
                        try:
                            # 检查图片是否存在且有效
                            if os.path.exists(filename):
                                # 获取图片尺寸以保持比例
                                img = Image.open(filename)
                                img_width, img_height = img.size
                                aspect = img_height / float(img_width)
                                
                                # 设置PDF中图片的宽度（例如页面宽度的80%）
                                pdf_img_width = 5 * inch
                                pdf_img_height = pdf_img_width * aspect

                                
                                if pdf_img_height > 636:
                                    pdf_img_height = 636
                                    pdf_img_width = pdf_img_height / aspect

                                
                                # 添加图片到PDF
                                story.append(RLImage(filename, width=pdf_img_width, height=pdf_img_height))
                                story.append(Paragraph("<br/><br/>", styles['Normal']))  # 图片后添加间距
                        except Exception as e:
                            print(f"处理图片失败: {filename}, 错误: {e}")
                            
            doc.build(story)
            print("生成PDF文档成功！")
        return pdf_file_name


    

if __name__ == '__main__':
    # 使用示例
    url = 'https://baijiahao.baidu.com/s?id=1791683653520481221&wfr=spider&for=pc'
    driver = ChromeDriver()
    driver.get(url)

    page_source = driver.page_source
    title_xpath = NewsCrawlConfig.KR36['title_xpath']
    source_path = NewsCrawlConfig.KR36['source_path']
    release_time_xpath = NewsCrawlConfig.KR36['release_time_xpath']
    content_list_xpath = NewsCrawlConfig.KR36['content_list_xpath']
    list_type = "div"
    # img_xpath = "//div[@class='article-content']/img"
    parser = WebPageParser(page_source)
    data = parser.get_page_info(title_xpath, source_path, release_time_xpath, content_list_xpath, list_type)
    print(data)


