# 网页管理服务文档

## 概述

网页管理服务提供了完整的网页下载、管理、重试和入库功能，支持批量操作和后台任务处理。

## 目录结构

```
service_crawer_manage/
├── __init__.py
├── api/
│   ├── __init__.py
│   └── webpage_api.py          # API接口定义
├── entity/
│   ├── __init__.py
│   └── webpage_entity.py       # 数据库实体类
├── service/
│   ├── __init__.py
│   ├── webpage_download_service.py    # 下载服务
│   ├── webpage_query_service.py       # 查询服务
│   ├── webpage_file_service.py        # 文件服务
│   ├── webpage_retry_service.py       # 重试服务
│   └── webpage_storage_service.py     # 入库服务
├── ddl/
│   └── webpage_manage_ddl.sql  # 数据库DDL脚本
├── test_webpage_service.py     # 测试脚本
└── README.md                   # 本文档
```

## 数据库设计

### 1. 网页下载任务表 (webpage_download_job)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| job_id | VARCHAR(64) | 任务ID（主键） |
| job_name | VARCHAR(255) | 任务名称 |
| job_status | VARCHAR(20) | 任务状态 |
| total_count | INT | 总网页数量 |
| success_count | INT | 成功下载数量 |
| failed_count | INT | 失败下载数量 |
| create_time | DATETIME | 创建时间 |
| start_time | DATETIME | 开始时间 |
| end_time | DATETIME | 结束时间 |
| update_time | DATETIME | 更新时间 |

### 2. 网页下载详情表 (webpage_download_detail)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 自增ID |
| job_id | VARCHAR(64) | 任务ID |
| webpage_url | VARCHAR(2048) | 网页地址 |
| webpage_name | VARCHAR(500) | 保存网页的名字 |
| data_type | VARCHAR(100) | 数据类型 |
| download_status | VARCHAR(20) | 下载状态 |
| file_path | VARCHAR(1024) | 文件保存地址 |
| file_size | BIGINT | 文件大小（字节） |
| error_message | TEXT | 错误信息 |
| retry_count | INT | 重试次数 |
| create_time | DATETIME | 创建时间 |
| download_time | DATETIME | 下载完成时间 |
| update_time | DATETIME | 更新时间 |

## API接口

### 1. 创建网页下载任务
- **接口**: `POST /api/webpage/download/create`
- **功能**: 创建网页下载任务，支持单次或批量请求
- **入参**: 网页地址、数据类型、保存网页的名字（数组形式）
- **出参**: job_id

### 2. 查询任务状态
- **接口**: `GET /api/webpage/job/{job_id}/status`
- **功能**: 查询任务执行状态和统计信息

### 3. 查询任务详情
- **接口**: `GET /api/webpage/job/{job_id}/details`
- **功能**: 查询任务的详细下载记录（分页）

### 4. 查询任务列表
- **接口**: `GET /api/webpage/jobs`
- **功能**: 查询所有任务列表（分页）

### 5. 下载文件（精确匹配）
- **接口**: `GET /api/webpage/download/file`
- **功能**: 通过job_id + 网页地址 + 保存网页名字下载文件

### 6. 下载文件（按URL）
- **接口**: `GET /api/webpage/download/file/by-url`
- **功能**: 通过网页地址下载文件（返回最新的成功下载）

### 7. 查询文件列表
- **接口**: `GET /api/webpage/job/{job_id}/files`
- **功能**: 通过job_id查询文件列表信息

### 8. 重新下载
- **接口**: `POST /api/webpage/retry`
- **功能**: 重新下载失败的网页，支持多种方式

### 9. 文件入库
- **接口**: `POST /api/webpage/storage`
- **功能**: 文件入库，支持批量入库和指定文件入库

## 使用说明

### 1. 数据库初始化

首先执行DDL脚本创建数据库表：

```sql
-- 执行 ddl/webpage_manage_ddl.sql 中的脚本
```

### 2. 创建下载任务

```python
from service_crawer_manage.service.webpage_download_service import WebpageDownloadService

# 准备网页请求数据
webpage_requests = [
    {
        'url': 'https://example.com/page1',
        'data_type': '资讯',
        'name': '示例页面1'
    },
    {
        'url': 'https://example.com/page2',
        'data_type': '政策',
        'name': '示例页面2'
    }
]

# 创建下载任务
result = WebpageDownloadService.create_download_job(
    webpage_requests=webpage_requests,
    job_name="示例下载任务"
)

print(f"任务创建成功，Job ID: {result['job_id']}")
```

### 3. 查询任务状态

```python
from service_crawer_manage.service.webpage_query_service import WebpageQueryService

# 查询任务状态
status = WebpageQueryService.query_job_status(job_id)
print(f"任务状态: {status['job_status']}")
print(f"成功数量: {status['success_count']}")
print(f"失败数量: {status['failed_count']}")
```

### 4. 重试失败的下载

```python
from service_crawer_manage.service.webpage_retry_service import WebpageRetryService

# 重试指定任务的失败下载
result = WebpageRetryService.retry_failed_downloads(job_id=job_id)
print(f"重试任务启动: {result['message']}")
```

### 5. 文件入库

```python
from service_crawer_manage.service.webpage_storage_service import WebpageStorageService

# 批量入库指定任务的文件
result = WebpageStorageService.store_files_by_job_id(job_id)
print(f"入库任务启动: {result['message']}")
```

## 注意事项

1. **环境要求**: 确保conda环境为`tiance-industry-finance`
2. **依赖关系**: 依赖现有的`script/download_pdfs.py`、`script/retry_failed_downloads.py`和`script/upload_files_and_infos(1).py`
3. **文件存储**: 下载的文件默认保存在`./temp_webpage_downloads`目录
4. **后台任务**: 下载、重试和入库都是后台异步执行
5. **会话管理**: 已修复SQLAlchemy会话绑定问题，支持跨线程操作
6. **向后兼容**: 保持原有脚本文件的功能不变

## 状态说明

### 任务状态 (job_status)
- `CREATED`: 已创建
- `RUNNING`: 运行中
- `COMPLETED`: 已完成
- `FAILED`: 失败

### 下载状态 (download_status)
- `PENDING`: 未下载
- `DOWNLOADING`: 正在下载
- `SUCCESS`: 下载成功
- `FAILED`: 下载失败
- `STORED`: 已入库
