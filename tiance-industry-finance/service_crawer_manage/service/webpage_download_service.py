#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 15:00:00
# <AUTHOR> Assistant
# @File         : webpage_download_service.py
# @Description  : 网页下载服务类
"""

import os
import asyncio
import threading
from datetime import datetime
from typing import List, Dict, Any, Union
from utils.sql_util import SQLUtil
from utils.log_util import LogUtil
from utils.uuid_util import UuidUtil
from service_crawer_manage.entity.webpage_entity import WebpageDownloadJob, WebpageDownloadDetail, JobStatus, DownloadStatus


class WebpageDownloadService:
    """网页下载服务类"""
    
    # 自定义临时目录
    TEMP_DOWNLOAD_DIR = "./temp_webpage_downloads"
    
    @staticmethod
    def create_download_job(webpage_requests: List[Dict[str, Any]], job_name: str = None) -> Dict[str, Any]:
        """
        创建网页下载任务
        
        Args:
            webpage_requests: 网页请求列表，每个元素包含 url, data_type, name
            job_name: 任务名称（可选）
            
        Returns:
            包含job_id的响应字典
        """
        try:
            SQLUtil.connect()
            
            # 生成任务ID
            job_id = UuidUtil.get_uuid()
            
            # 验证输入数据
            if not webpage_requests or not isinstance(webpage_requests, list):
                raise ValueError("网页请求列表不能为空")
            
            validated_requests = []
            for i, req in enumerate(webpage_requests):
                if not isinstance(req, dict):
                    raise ValueError(f"第{i+1}个请求格式错误，必须是字典")
                
                url = req.get('url', '').strip()
                data_type = req.get('data_type', '').strip()
                name = req.get('name', '').strip()
                
                if not url:
                    raise ValueError(f"第{i+1}个请求的网页地址不能为空")
                if not data_type:
                    raise ValueError(f"第{i+1}个请求的数据类型不能为空")
                if not name:
                    raise ValueError(f"第{i+1}个请求的保存名称不能为空")
                
                validated_requests.append({
                    'url': url,
                    'data_type': data_type,
                    'name': name
                })
            
            # 创建任务记录
            job_data = {
                'job_id': job_id,
                'job_name': job_name or f"网页下载任务_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'job_status': JobStatus.CREATED.value,
                'total_count': len(validated_requests),
                'success_count': 0,
                'failed_count': 0
            }
            
            SQLUtil.insert_one(WebpageDownloadJob, job_data)
            
            # 创建详情记录
            detail_data_list = []
            for req in validated_requests:
                detail_data = {
                    'job_id': job_id,
                    'webpage_url': req['url'],
                    'webpage_name': req['name'],
                    'data_type': req['data_type'],
                    'download_status': DownloadStatus.PENDING.value
                }
                detail_data_list.append(detail_data)
            
            SQLUtil.insert_many(WebpageDownloadDetail, detail_data_list)
            
            # 启动后台下载任务
            WebpageDownloadService._start_background_download(job_id)
            
            LogUtil.info(f"网页下载任务创建成功: job_id={job_id}, 总数量={len(validated_requests)}")
            
            return {
                "success": True,
                "job_id": job_id,
                "message": f"任务创建成功，共{len(validated_requests)}个网页待下载",
                "total_count": len(validated_requests)
            }
            
        except Exception as e:
            error_msg = f"创建网页下载任务失败: {str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            try:
                SQLUtil.close()
            except:
                pass
    
    @staticmethod
    def _start_background_download(job_id: str):
        """启动后台下载任务"""
        def run_download():
            try:
                # 在新线程中运行异步下载
                asyncio.run(WebpageDownloadService._execute_download_job(job_id))
            except Exception as e:
                LogUtil.error(f"后台下载任务执行失败: job_id={job_id}, error={str(e)}")
        
        # 启动后台线程
        download_thread = threading.Thread(target=run_download, daemon=True)
        download_thread.start()
        LogUtil.info(f"后台下载任务已启动: job_id={job_id}")
    
    @staticmethod
    async def _execute_download_job(job_id: str):
        """执行下载任务"""
        try:
            SQLUtil.connect()
            
            # 更新任务状态为运行中
            SQLUtil.update_by_id(WebpageDownloadJob, job_id, {
                'job_status': JobStatus.RUNNING.value,
                'start_time': datetime.now()
            })
            
            # 获取待下载的网页列表
            details = SQLUtil.query_by_column(
                WebpageDownloadDetail,
                'job_id',
                job_id,
                exact_match=True
            )
            
            if not details:
                raise Exception(f"未找到任务详情: job_id={job_id}")
            
            # 确保临时目录存在
            os.makedirs(WebpageDownloadService.TEMP_DOWNLOAD_DIR, exist_ok=True)
            
            success_count = 0
            failed_count = 0
            
            # 导入下载模块
            from script.download_pdfs import download_and_convert_to_pdf_pyppeteer
            
            for detail in details:
                try:
                    # 更新状态为下载中
                    SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                        'download_status': DownloadStatus.DOWNLOADING.value
                    })
                    
                    # 执行下载
                    success = await download_and_convert_to_pdf_pyppeteer(
                        detail.webpage_name,
                        detail.webpage_url,
                        WebpageDownloadService.TEMP_DOWNLOAD_DIR
                    )
                    
                    if success:
                        # 检查文件是否存在并获取文件信息
                        file_path = os.path.join(WebpageDownloadService.TEMP_DOWNLOAD_DIR, f"{detail.webpage_name}.pdf")
                        file_size = os.path.getsize(file_path) if os.path.exists(file_path) else None
                        
                        # 更新为成功状态
                        SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                            'download_status': DownloadStatus.SUCCESS.value,
                            'file_path': file_path,
                            'file_size': file_size,
                            'download_time': datetime.now(),
                            'error_message': None
                        })
                        success_count += 1
                        LogUtil.info(f"网页下载成功: {detail.webpage_name}")
                    else:
                        # 更新为失败状态
                        SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                            'download_status': DownloadStatus.FAILED.value,
                            'error_message': "下载失败"
                        })
                        failed_count += 1
                        LogUtil.error(f"网页下载失败: {detail.webpage_name}")
                        
                except Exception as e:
                    # 更新为失败状态
                    SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                        'download_status': DownloadStatus.FAILED.value,
                        'error_message': str(e)
                    })
                    failed_count += 1
                    LogUtil.error(f"网页下载异常: {detail.webpage_name}, error={str(e)}")
                
                # 请求间隔
                await asyncio.sleep(2)
            
            # 更新任务完成状态
            job_status = JobStatus.COMPLETED.value if failed_count == 0 else JobStatus.FAILED.value
            SQLUtil.update_by_id(WebpageDownloadJob, job_id, {
                'job_status': job_status,
                'success_count': success_count,
                'failed_count': failed_count,
                'end_time': datetime.now()
            })
            
            LogUtil.info(f"下载任务完成: job_id={job_id}, 成功={success_count}, 失败={failed_count}")
            
        except Exception as e:
            # 更新任务为失败状态
            try:
                SQLUtil.update_by_id(WebpageDownloadJob, job_id, {
                    'job_status': JobStatus.FAILED.value,
                    'end_time': datetime.now()
                })
            except:
                pass
            LogUtil.error(f"执行下载任务失败: job_id={job_id}, error={str(e)}")
        finally:
            try:
                SQLUtil.close()
            except:
                pass
