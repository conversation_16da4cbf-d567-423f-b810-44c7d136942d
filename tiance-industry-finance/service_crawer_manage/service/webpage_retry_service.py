#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 16:30:00
# <AUTHOR> Assistant
# @File         : webpage_retry_service.py
# @Description  : 网页重新下载服务类
"""

import os
import asyncio
import threading
from datetime import datetime
from typing import List, Dict, Any, Union
from utils.sql_util import SQLUtil
from utils.log_util import LogUtil
from service_crawer_manage.entity.webpage_entity import WebpageDownloadDetail, DownloadStatus


class WebpageRetryService:
    """网页重新下载服务类"""
    
    # 自定义临时目录
    TEMP_DOWNLOAD_DIR = "./temp_webpage_downloads"
    
    @staticmethod
    def retry_failed_downloads(job_id: str = None, webpage_urls: List[str] = None) -> Dict[str, Any]:
        """
        重新下载失败的网页
        
        Args:
            job_id: 任务ID（可选，如果提供则重试该任务的所有失败下载）
            webpage_urls: 网页地址列表（可选，如果提供则重试指定的网页）
            
        Returns:
            重试结果
        """
        try:
            SQLUtil.connect()
            
            # 获取需要重试的下载记录
            if job_id:
                # 按任务ID重试
                retry_details = SQLUtil.query_by_multiple_columns(
                    WebpageDownloadDetail,
                    {
                        'job_id': job_id,
                        'download_status': DownloadStatus.FAILED.value
                    }
                )
                for detail in retry_details:
                    SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                        'download_status': DownloadStatus.DOWNLOADING,
                        'error_message': None
                    })
                if not retry_details:
                    return {
                        "success": True,
                        "message": f"任务 {job_id} 没有失败的下载记录",
                        "retry_count": 0
                    }
            elif webpage_urls:
                # 按网页地址重试
                retry_details = []
                for url in webpage_urls:
                    details = SQLUtil.query_by_multiple_columns(
                        WebpageDownloadDetail,
                        {
                            'webpage_url': url
                        }
                    )
                    for detail in details:
                        SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                        'download_status': DownloadStatus.DOWNLOADING,
                        'error_message': None
                    })
                    retry_details.extend(details)
                
                if not retry_details:
                    return {
                        "success": True,
                        "message": "指定的网页地址没有失败的下载记录",
                        "retry_count": 0
                    }
            else:
                raise ValueError("必须提供 job_id 或 webpage_urls 参数")
            
            # 启动后台重试任务
            WebpageRetryService._start_background_retry(retry_details)
            
            LogUtil.info(f"重新下载任务启动成功，共 {len(retry_details)} 个网页待重试")
            
            return {
                "success": True,
                "message": f"重新下载任务启动成功，共 {len(retry_details)} 个网页待重试",
                "retry_count": len(retry_details)
            }
            
        except Exception as e:
            error_msg = f"启动重新下载失败: {str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            try:
                SQLUtil.close()
            except:
                pass
    
    @staticmethod
    def _start_background_retry(retry_details: List[WebpageDownloadDetail]):
        """启动后台重试任务"""
        def run_retry():
            try:
                # 在新线程中运行异步重试
                asyncio.run(WebpageRetryService._execute_retry_downloads(retry_details))
            except Exception as e:
                LogUtil.error(f"后台重试任务执行失败: error={str(e)}")
        
        # 启动后台线程
        retry_thread = threading.Thread(target=run_retry, daemon=True)
        retry_thread.start()
        LogUtil.info(f"后台重试任务已启动，共 {len(retry_details)} 个网页")
    
    @staticmethod
    async def _execute_retry_downloads(retry_details: List[WebpageDownloadDetail]):
        """执行重试下载"""
        try:
            SQLUtil.connect()
            
            # 确保临时目录存在
            os.makedirs(WebpageRetryService.TEMP_DOWNLOAD_DIR, exist_ok=True)
            
            success_count = 0
            failed_count = 0
            
            # 导入重试下载模块
            from script.retry_failed_downloads import download_with_fallback_strategy
            
            for detail in retry_details:
                try:
                    # 更新重试次数和状态
                    SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                        'download_status': DownloadStatus.DOWNLOADING,
                        'retry_count': detail.retry_count + 1,
                        'error_message': None
                    })
                    
                    # 删除原有的失败文件（如果存在）
                    if detail.file_path and os.path.exists(detail.file_path):
                        try:
                            os.remove(detail.file_path)
                            LogUtil.info(f"删除原有失败文件: {detail.file_path}")
                        except Exception as e:
                            LogUtil.warning(f"删除原有文件失败: {detail.file_path}, error={str(e)}")
                    
                    # 执行重试下载
                    success = await download_with_fallback_strategy(
                        detail.webpage_name,
                        detail.webpage_url,
                        WebpageRetryService.TEMP_DOWNLOAD_DIR
                    )
                    
                    if success:
                        # 检查文件是否存在并获取文件信息
                        file_path = os.path.join(WebpageRetryService.TEMP_DOWNLOAD_DIR, f"{detail.webpage_name}.pdf")
                        file_size = os.path.getsize(file_path) if os.path.exists(file_path) else None
                        
                        # 更新为成功状态
                        SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                            'download_status': DownloadStatus.SUCCESS,
                            'file_path': file_path,
                            'file_size': file_size,
                            'download_time': datetime.now(),
                            'error_message': None
                        })
                        success_count += 1
                        LogUtil.info(f"网页重试下载成功: {detail.webpage_name}")
                    else:
                        # 更新为失败状态
                        SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                            'download_status': DownloadStatus.FAILED,
                            'error_message': "重试下载失败"
                        })
                        failed_count += 1
                        LogUtil.error(f"网页重试下载失败: {detail.webpage_name}")
                        
                except Exception as e:
                    # 更新为失败状态
                    SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
                        'download_status': DownloadStatus.FAILED,
                        'error_message': f"重试异常: {str(e)}"
                    })
                    failed_count += 1
                    LogUtil.error(f"网页重试下载异常: {detail.webpage_name}, error={str(e)}")
                
                # 请求间隔
                await asyncio.sleep(2)
            
            LogUtil.info(f"重试下载任务完成: 成功={success_count}, 失败={failed_count}")
            
        except Exception as e:
            LogUtil.error(f"执行重试下载任务失败: error={str(e)}")
        finally:
            try:
                SQLUtil.close()
            except:
                pass
    
    @staticmethod
    def retry_by_detail_ids(detail_ids: List[int]) -> Dict[str, Any]:
        """
        通过详情ID列表重新下载
        
        Args:
            detail_ids: 详情ID列表
            
        Returns:
            重试结果
        """
        try:
            SQLUtil.connect()
            
            # 获取需要重试的下载记录
            retry_details = []
            for detail_id in detail_ids:
                detail = SQLUtil.get_data_by_id(WebpageDownloadDetail, detail_id)
                SQLUtil.update_by_id(WebpageDownloadDetail, detail_id, {
                    'download_status': DownloadStatus.DOWNLOADING,
                    'error_message': None
                })
                retry_details.append(detail)
            
            if not retry_details:
                return {
                    "success": True,
                    "message": "指定的记录没有找到相关",
                    "retry_count": 0
                }
            
            # 启动后台重试任务
            WebpageRetryService._start_background_retry(retry_details)
            
            LogUtil.info(f"按详情ID重新下载任务启动成功，共 {len(retry_details)} 个网页待重试")
            
            return {
                "success": True,
                "message": f"重新下载任务启动成功，共 {len(retry_details)} 个网页待重试",
                "retry_count": len(retry_details)
            }
            
        except Exception as e:
            error_msg = f"按详情ID启动重新下载失败: {str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            try:
                SQLUtil.close()
            except:
                pass
