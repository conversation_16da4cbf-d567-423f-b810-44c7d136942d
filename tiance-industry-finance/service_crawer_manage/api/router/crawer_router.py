#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 17:30:00
# <AUTHOR> Assistant
# @File         : crawer_api.py
# @Description  : 网页管理API接口
"""

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import FileResponse
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

from service_crawer_manage.service.webpage_download_service import WebpageDownloadService
from service_crawer_manage.service.webpage_query_service import WebpageQueryService
from service_crawer_manage.service.webpage_file_service import WebpageFileService
from service_crawer_manage.service.webpage_retry_service import WebpageRetryService
from service_crawer_manage.service.webpage_storage_service import WebpageStorageService
from service_crawer_manage.entity.webpage_entity import WebpageRequest, BatchWebpageRequest, RetryRequest, StorageRequest

# 创建路由器
router = APIRouter()



# 接口1: 创建网页下载任务
@router.post("/download/create", summary="创建网页下载任务")
async def create_download_job(request: BatchWebpageRequest):
    """
    创建网页下载任务
    
    支持单次请求或批量请求：
    - 入参：网页地址，数据类型，保存网页的名字（可以为单次请求或者批量请求数组形式）
    - 出参：job_id
    - 过程：
      1. 创建job_id，保存到MySQL
      2. 保存网页下载详情到MySQL
      3. 启动后台下载任务
      4. 返回job_id
    """
    try:
        # 转换请求格式
        webpage_requests = []
        for webpage in request.webpages:
            webpage_requests.append({
                'url': webpage.url,
                'data_type': webpage.data_type,
                'name': webpage.name
            })
        
        # 调用服务创建任务
        result = WebpageDownloadService.create_download_job(
            webpage_requests=webpage_requests,
            job_name=request.job_name
        )
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 接口2: 查询任务状态
@router.get("/job/{job_id}/status", summary="查询任务状态")
async def get_job_status(job_id: str):
    """
    查询任务状态
    
    根据job_id查询任务的执行状态和统计信息
    """
    try:
        result = WebpageQueryService.query_job_status(job_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 接口3: 查询任务详情
@router.get("/job/{job_id}/details", summary="查询任务详情")
async def get_job_details(
    job_id: str,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小")
):
    """
    查询任务详情（分页）
    
    根据job_id查询任务的详细下载记录
    """
    try:
        result = WebpageQueryService.query_job_details(job_id, page, page_size)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 接口4: 查询任务列表
@router.get("/jobs", summary="查询任务列表")
async def get_jobs_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    job_status: Optional[str] = Query(None, description="任务状态过滤")
):
    """
    查询任务列表（分页）
    
    查询所有任务的列表，支持按状态过滤
    """
    try:
        result = WebpageQueryService.query_jobs_list(page, page_size, job_status)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 接口5: 下载文件 - 通过job_id + 网页地址 + 保存网页名字
@router.get("/download/file", summary="下载文件（精确匹配）")
async def download_file_exact(
    job_id: str = Query(..., description="任务ID"),
    webpage_url: str = Query(..., description="网页地址"),
    webpage_name: str = Query(..., description="保存网页的名字")
):
    """
    通过job_id + 网页地址 + 保存网页名字下载文件
    """
    try:
        file_path, filename = WebpageFileService.download_file_by_job_and_name(
            job_id, webpage_url, webpage_name
        )
        return WebpageFileService.get_file_response(file_path, filename)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 接口6: 下载文件 - 通过网页地址
@router.get("/download/file/by-url", summary="下载文件（按URL）")
async def download_file_by_url(
    webpage_url: str = Query(..., description="网页地址")
):
    """
    通过网页地址下载文件（返回最新的成功下载）
    """
    try:
        file_path, filename = WebpageFileService.download_file_by_url(webpage_url)
        return WebpageFileService.get_file_response(file_path, filename)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 接口7: 查询文件列表 - 通过job_id
@router.get("/job/{job_id}/files", summary="查询文件列表")
async def get_job_files(job_id: str):
    """
    通过job_id查询文件列表信息
    """
    try:
        result = WebpageFileService.download_file_by_job_id(job_id)
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 接口8: 重新下载
@router.post("/retry", summary="重新下载")
async def retry_downloads(request: RetryRequest):
    """
    重新下载失败的网页
    
    支持三种方式：
    1. 通过job_id重试该任务的所有失败下载
    2. 通过webpage_urls重试指定的网页地址
    3. 通过detail_ids重试指定的详情记录
    """
    try:
        if request.detail_ids:
            # 按详情ID重试
            result = WebpageRetryService.retry_by_detail_ids(request.detail_ids)
        else:
            # 按任务ID或网页地址重试
            result = WebpageRetryService.retry_failed_downloads(
                job_id=request.job_id,
                webpage_urls=request.webpage_urls
            )
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# 接口9: 文件入库
@router.post("/storage", summary="文件入库")
async def store_files(request: StorageRequest):
    """
    文件入库
    
    支持两种方式：
    1. 通过job_id批量入库该任务的所有成功下载文件
    2. 通过网页地址+保存名称入库指定文件
    """
    try:
        if request.job_id:
            # 按任务ID入库
            result = WebpageStorageService.store_files_by_job_id(request.job_id)
        elif request.url_name_pairs:
            # 按网页地址和名称入库
            result = WebpageStorageService.store_files_by_urls_and_names(request.url_name_pairs)
        else:
            raise HTTPException(
                status_code=400,
                detail="必须提供 job_id 或 url_name_pairs 参数"
            )
        
        return result
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
