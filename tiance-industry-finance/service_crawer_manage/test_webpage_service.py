#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 18:00:00
# <AUTHOR> Assistant
# @File         : test_webpage_service.py
# @Description  : 网页服务测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from service_crawer_manage.service.webpage_download_service import WebpageDownloadService

def test_create_download_job():
    """测试创建下载任务"""
    try:
        # 测试数据
        webpage_requests = [
            {
                'url': 'https://www.example.com/test1',
                'data_type': '资讯',
                'name': '测试网页1'
            },
            {
                'url': 'https://www.example.com/test2',
                'data_type': '政策',
                'name': '测试网页2'
            }
        ]
        
        # 创建下载任务
        result = WebpageDownloadService.create_download_job(
            webpage_requests=webpage_requests,
            job_name="测试任务"
        )
        
        print("创建下载任务成功:")
        print(f"Job ID: {result['job_id']}")
        print(f"消息: {result['message']}")
        print(f"总数量: {result['total_count']}")
        
        return result['job_id']
        
    except Exception as e:
        print(f"创建下载任务失败: {str(e)}")
        return None

if __name__ == "__main__":
    print("开始测试网页服务...")
    
    # 测试创建下载任务
    job_id = test_create_download_job()
    
    if job_id:
        print(f"\n测试成功！任务ID: {job_id}")
        print("请检查数据库中的记录是否正确创建。")
    else:
        print("\n测试失败！")
