{
  "webpages": [
    {
      "data_type": "政策",
      "name": "广东省2020年水产绿色健康养殖“五大行动”实施方案",
      "release_time_str": "2025-07-16",
      "url": "http://www.nftec.agri.cn/zthd/wdxd/202004/P020200520541415122780.pdf"
    }
  ],
  "job_name": "测试任务"
}

{
  "webpages": [
    {
      "data_type": "资讯",
      "name": "遂溪乐民镇：“花蟹盛宴”亮品牌 “蓝色经济”促发展",
      "release_time_str": "2025-07-16",
      "url": "https://static.nfapp.southcn.com/content/202308/22/c8018974.html"
    }
  ],
  "job_name": "测试任务2"
}


{
  "success": true,
  "job_id": "9cc00d9b50a34ed884eb201af37b19a4",
  "message": "任务创建成功，共1个网页待下载",
  "total_count": 1
}

{
  "success": true,
  "job_id": "a24f07d1819e40b8ad24008360a94e27",
  "message": "任务创建成功，共1个网页待下载",
  "total_count": 1
}

{
  "job_id": "9cc00d9b50a34ed884eb201af37b19a4"
}

{
  "webpage_urls": [
    "http://www.nftec.agri.cn/zthd/wdxd/202004/P020200520541415122780.pdf"
  ]
}

{
  "job_id": "9cc00d9b50a34ed884eb201af37b19a4",
  "webpage_urls": [
    "http://example_retry1.com",
    "http://example_retry2.com"
  ],
  "detail_ids": [
    1001,
    1002
  ]
}




{
  "job_id": "9cc00d9b50a34ed884eb201af37b19a4"
}

{
  "job_id": "job_456",
  "url_name_pairs": [
    {
      "name": "网页A",
      "url": "http://example_store1.com"
    }
  ]
}


Traceback (most recent call last):
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/pool/base.py", line 986, in _finalize_fairy
    fairy._reset(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/pool/base.py", line 1432, in _reset
    pool._dialect.do_rollback(self)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/default.py", line 699, in do_rollback
    dbapi_connection.rollback()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 492, in rollback
    self._execute_command(COMMAND.COM_QUERY, "ROLLBACK")
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 843, in _execute_command
    raise err.InterfaceError(0, "")
pymysql.err.InterfaceError: (0, '')
2025-07-17 11:40:40,673 - ERROR - Pyppeteer error converting https://static.nfapp.southcn.com/content/202308/22/c8018974.html to PDF: signal only works in main thread of the main interpreter
2025-07-17 11:40:40,910 - ERROR - 网页下载失败: 遂溪乐民镇：“花蟹盛宴”亮品牌 “蓝色经济”促发展
NoneType: None
2025-07-17 11:40:43,180 - INFO - 下载任务完成: job_id=a24f07d1819e40b8ad24008360a94e27, 成功=0, 失败=1
2025-07-17 11:41:43,107 - INFO - 查询任务状态成功: job_id=a24f07d1819e40b8ad24008360a94e27