#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 15:00:00
# <AUTHOR> Assistant
# @File         : webpage_entity.py
# @Description  : 网页管理相关实体类
"""

from sqlalchemy import Column, String, Integer, DateTime, Text, BigInteger
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from enum import Enum as PyEnum

# 创建 SQLAlchemy 的基类
Base = declarative_base()


class JobStatus(PyEnum):
    """任务状态枚举"""
    CREATED = "CREATED"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class DownloadStatus(PyEnum):
    """下载状态枚举"""
    PENDING = "PENDING"
    DOWNLOADING = "DOWNLOADING"
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"
    STORED = "STORED"


class WebpageDownloadJob(Base):
    """网页下载任务表 ORM 模型"""
    __tablename__ = 'webpage_download_job'
    __table_args__ = {
        'comment': '网页下载任务表',
        'mysql_engine': 'InnoDB',
        'mysql_charset': 'utf8mb4',
        'mysql_collate': 'utf8mb4_unicode_ci'
    }
    
    job_id = Column(String(64), primary_key=True, nullable=False, comment='任务ID（主键）')
    job_name = Column(String(255), nullable=True, comment='任务名称')
    job_status = Column(String(20), nullable=False, default='CREATED', comment='任务状态')
    total_count = Column(Integer, default=0, comment='总网页数量')
    success_count = Column(Integer, default=0, comment='成功下载数量')
    failed_count = Column(Integer, default=0, comment='失败下载数量')
    create_time = Column(DateTime, nullable=False, default=func.now(), comment='创建时间')
    start_time = Column(DateTime, nullable=True, comment='开始时间')
    end_time = Column(DateTime, nullable=True, comment='结束时间')
    update_time = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(), comment='更新时间')


class WebpageDownloadDetail(Base):
    """网页下载详情表 ORM 模型"""
    __tablename__ = 'webpage_download_detail'
    __table_args__ = {
        'comment': '网页下载详情表',
        'mysql_engine': 'InnoDB',
        'mysql_charset': 'utf8mb4',
        'mysql_collate': 'utf8mb4_unicode_ci'
    }
    
    id = Column(BigInteger, primary_key=True, autoincrement=True, comment='自增ID')
    job_id = Column(String(64), nullable=False, comment='任务ID')
    webpage_url = Column(String(2048), nullable=False, comment='网页地址')
    webpage_name = Column(String(500), nullable=False, comment='保存网页的名字')
    data_type = Column(String(100), nullable=False, comment='数据类型')
    download_status = Column(String(20), nullable=False, default='PENDING', comment='下载状态')
    file_path = Column(String(1024), nullable=True, comment='文件保存地址')
    file_size = Column(BigInteger, nullable=True, comment='文件大小（字节）')
    error_message = Column(Text, nullable=True, comment='错误信息')
    retry_count = Column(Integer, default=0, comment='重试次数')
    create_time = Column(DateTime, nullable=False, default=func.now(), comment='创建时间')
    download_time = Column(DateTime, nullable=True, comment='下载完成时间')
    release_time = Column(DateTime, nullable=True, comment='文件发布时间')
    update_time = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(), comment='更新时间')

from fastapi import APIRouter, HTTPException, Query
from fastapi.responses import FileResponse
from typing import List, Dict, Any, Optional
from pydantic import BaseModel


# request entity
from typing import List, Optional, Dict
from pydantic import BaseModel, Field


# 请求模型
class WebpageRequest(BaseModel):
    """单个网页请求模型"""
    url: str = Field(..., example="http://example.com", description="网页网址")
    data_type: str = Field(..., example="新闻", description="数据类型")
    name: str = Field(..., example="某网页名称", description="网页名称")
    release_time_str: str = Field(..., example="2024-09-15", description="发布日期字符串，期望格式如 'YYYY-MM-DD'")


class BatchWebpageRequest(BaseModel):
    """批量网页请求模型"""
    webpages: List[WebpageRequest] = Field(..., example=[{"url": "http://example1.com", "data_type": "新闻", "name": "网页1", "release_time_str": "2024-09-15"}], description="网页请求列表")
    job_name: Optional[str] = Field(None, example="某批量任务", description="任务名称，可选")


class RetryRequest(BaseModel):
    """重试请求模型"""
    job_id: Optional[str] = Field(None, example="job_123", description="任务 ID，可选")
    webpage_urls: Optional[List[str]] = Field(None, example=["http://example_retry1.com", "http://example_retry2.com"], description="需要重试的网页网址列表，可选")
    detail_ids: Optional[List[int]] = Field(None, example=[1001, 1002], description="详情 ID 列表，可选")


class StorageRequest(BaseModel):
    """入库请求模型"""
    job_id: Optional[str] = Field(None, example="job_456", description="任务 ID，可选")
    url_name_pairs: Optional[List[Dict[str, str]]] = Field(None, example=[{"url": "http://example_store1.com", "name": "网页A"}], description="网址 - 名称键值对列表，可选")