-- 网页管理相关表DDL脚本

-- 1. 网页下载任务表
CREATE TABLE IF NOT EXISTS `webpage_download_job` (
    `job_id` VARCHAR(64) NOT NULL COMMENT '任务ID（主键）',
    `job_name` VARCHAR(255) DEFAULT NULL COMMENT '任务名称',
    `job_status` ENUM('CREATED', 'RUNNING', 'COMPLETED', 'FAILED') NOT NULL DEFAULT 'CREATED' COMMENT '任务状态：CREATED-已创建，RUNNING-运行中，COMPLETED-已完成，FAILED-失败',
    `total_count` INT DEFAULT 0 COMMENT '总网页数量',
    `success_count` INT DEFAULT 0 COMMENT '成功下载数量',
    `failed_count` INT DEFAULT 0 COMMENT '失败下载数量',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `start_time` DATETIME DEFAULT NULL COMMENT '开始时间',
    `end_time` DATETIME DEFAULT NULL COMMENT '结束时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`job_id`),
    INDEX `idx_job_status` (`job_status`),
    INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网页下载任务表';

-- 2. 网页下载详情表
CREATE TABLE IF NOT EXISTS `webpage_download_detail` (
    `id` BIGINT AUTO_INCREMENT COMMENT '自增ID',
    `job_id` VARCHAR(64) NOT NULL COMMENT '任务ID',
    `webpage_url` VARCHAR(2048) NOT NULL COMMENT '网页地址',
    `webpage_name` VARCHAR(500) NOT NULL COMMENT '保存网页的名字',
    `data_type` VARCHAR(100) NOT NULL COMMENT '数据类型',
    `download_status` ENUM('PENDING', 'DOWNLOADING', 'SUCCESS', 'FAILED', 'STORED') NOT NULL DEFAULT 'PENDING' COMMENT '下载状态：PENDING-未下载，DOWNLOADING-正在下载，SUCCESS-下载成功，FAILED-下载失败，STORED-已入库',
    `file_path` VARCHAR(1024) DEFAULT NULL COMMENT '文件保存地址',
    `file_size` BIGINT DEFAULT NULL COMMENT '文件大小（字节）',
    `error_message` TEXT DEFAULT NULL COMMENT '错误信息',
    `retry_count` INT DEFAULT 0 COMMENT '重试次数',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `download_time` DATETIME DEFAULT NULL COMMENT '下载完成时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_job_url_name` (`job_id`, `webpage_url`(500), `webpage_name`(200)) COMMENT '同一任务下网页地址+名称唯一',
    INDEX `idx_job_id` (`job_id`),
    INDEX `idx_download_status` (`download_status`),
    INDEX `idx_data_type` (`data_type`),
    INDEX `idx_create_time` (`create_time`),
    FOREIGN KEY (`job_id`) REFERENCES `webpage_download_job`(`job_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网页下载详情表';
