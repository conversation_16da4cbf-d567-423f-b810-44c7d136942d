#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :research_report_structure.py
@Description  :
<AUTHOR>
@Date         :2024/11/13 17:55:32
'''
import asyncio
from datetime import datetime

from fastapi import APIRouter

from configs.model_config import ModelConfig
from entity.request_entity import ResearchReportStructureRequest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.cache_utils import CacheUtils
from utils.log_util import LogUtil
from service.kb_service import KbService
from service.llm_service import Llm_Service
from service.rerank_service import Rerank_Service
from entity.message_entity import SystemMessage, UserMessage, MessageConverter
from utils.minio_util import MinIoUtil
from utils.mongodb_util import MongodbUtil
from configs.collection_config import CollectionConfig
from configs.milvus_config import MilvusConfig
from configs.prompt_config import PromptConfig

router = APIRouter()


@router.post("/research_report_structure", summary="单研报产业链结构梳理agent")
async def research_report_structure(
    request: ResearchReportStructureRequest,
    is_use_cache: bool = False
) -> SuccessResponse | FalseResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request, is_use_cache=is_use_cache))
        # 初始化返回数据
        data = {"retrival_content": [], "result": ""}

        # 判断是否启用缓存
        if is_use_cache:
            # 启用缓存
            cached_data = CacheUtils.get_cache(
                CollectionConfig.CACHE_RESEARCH_REPORT_STRUCTURE,
                key=request.model_dump_json()
            )
            if cached_data is None:
                is_use_cache = False
                LogUtil.info("未命中缓存，将调用大模型生成结果")
            else:
                LogUtil.info("命中缓存，立即返回结果")
                data = cached_data
        if not is_use_cache:  # 再次判断
            # 未启用缓存
            LogUtil.info("1.根据mongodb_id检索得到向量数据库信息(包括向量数据库名,向量文本块字段名,标题)")

            doc_info = MongodbUtil.query_doc_by_id(request.collection_name, request.mongodb_id)
            title = doc_info["file"].get("file_title")
            file_source = doc_info["data"].get("data_source")
            milvus_collection_name = doc_info["milvus"].get("milvus_collection_name")  # milvus集合名

            milvus_field_name = MilvusConfig.MILVUS_DOC_CONTENT_FIELD_NAME

            LogUtil.info("2.检索向量数据库")
            kb_service = KbService()
            expr = f"file_title=='{title}' and file_source=='{file_source}'"
            # 构造检索语句
            retrieval_sentence = request.industry.replace("产业链", "") + "产业链的上游、中游、下游"
            doc_list = await kb_service.search_knowledge_by_question(collection_name=milvus_collection_name,
                                                                     question=retrieval_sentence,
                                                                     limit_top_k=request.k, expr=expr)
            LogUtil.info("3.拼接检索内容")
            content_list = []
            for item in doc_list:
                if item[milvus_field_name] not in content_list:
                    content_list.append(item[milvus_field_name])

            if not content_list:
                LogUtil.info("未检索到相关内容, 返回空")
                # 缓存
                CacheUtils.save_cache(
                    CollectionConfig.CACHE_RESEARCH_REPORT_STRUCTURE,
                    key=request.model_dump_json(),
                    value=data
                )
                # 记录返回日志
                LogUtil.log_json(describe="单研报产业链结构梳理agent请求返回结果", kwargs=data)
                return SuccessResponse(data=data)

            rerank_service = Rerank_Service()
            rerank_result = await rerank_service.rerank(retrieval_sentence, content_list)
            rerank_content = []
            for item in rerank_result["results"]:
                if item["relevance_score"] >= 0.5:
                    rerank_content.append(content_list[item["index"]])

            if not rerank_content:
                LogUtil.info("重排后未得到到相关内容, 返回空")
                # 缓存
                CacheUtils.save_cache(
                    CollectionConfig.CACHE_RESEARCH_REPORT_STRUCTURE,
                    key=request.model_dump_json(),
                    value=data
                )
                # 记录返回日志
                LogUtil.log_json(describe="单研报产业链结构梳理agent请求返回结果", kwargs=data)
                return SuccessResponse(data=data)

            LogUtil.info("4.组装提示词")
            messages = []
            messages.append(SystemMessage(request.system_prompt.format(industry=request.industry)))
            user_prompt = "\n\n\n\n".join(rerank_content)
            messages.append(UserMessage(user_prompt))
            messages = MessageConverter.convert_messages(messages)

            LogUtil.info("5.调用大模型生成回答")
            model = request.model
            llm_service = Llm_Service(model)
            answer = await llm_service.answer_question(messages, model, max_tokens=4096)
            data = {'retrival_content': rerank_content, 'result': answer}

            # 缓存
            CacheUtils.save_cache(
                CollectionConfig.CACHE_RESEARCH_REPORT_STRUCTURE,
                key=request.model_dump_json(),
                value=data
            )

        # 记录返回日志
        LogUtil.log_json(describe="单研报产业链结构梳理agent请求返回结果", kwargs=data)
        return SuccessResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)


async def main():
    for i in range(5):
        res = await asyncio.gather(research_report_structure(
            ResearchReportStructureRequest(
                model=ModelConfig.MAX_LLM_MODEL_NAME,
                mongodb_id="3ad4695c6cdc44e9ade70bf84d9c5049",
                k=5,
                industry="工业机器人",
                system_prompt=PromptConfig.RESEARCH_REPORT_STRUCTURE_DIRECT_SYSTEM_PROMPT
            ),
            is_use_cache=True
        ))
        print("\n".join(res[0].data["retrival_content"]))
        print(res[0].data["result"])
        print("========================\n\n")


if __name__ == "__main__":
    LogUtil.init('Test research_report_structure.py')
    MongodbUtil.connect()
    MinIoUtil.connect()

    asyncio.run(main())

    pass
