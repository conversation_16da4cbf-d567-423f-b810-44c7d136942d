#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :research_report_compose.py
@Description  :
<AUTHOR>
@Date         :2024/11/13 17:55:32
'''

import asyncio
import json
from fastapi import APIRouter
from tqdm import tqdm

from api.routes.research_report_compose import research_report_compose
from api.routes.research_report_structure import research_report_structure
from configs.collection_config import CollectionConfig
from entity.request_entity import SearchKeyCompaniesComposeRequest, ResearchReportComposeRequest, \
    ResearchReportStructureRequest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from api.routes.search_key_companies import search_key_companies
from entity.request_entity import SearchKeyCompaniesRequest
from configs.prompt_config import PromptConfig
from utils.minio_util import MinIoUtil
from utils.mongodb_util import MongodbUtil
from utils.tree_utils import TreeUtils
from utils.text_utils import TextUtil

router = APIRouter()


@router.post("/search_key_companies_compose", summary="多研报中心客群组合结果")
async def search_key_companies_compose(
    request: SearchKeyCompaniesComposeRequest,
    remove_duplicates_company_in_same_node: bool = True,  # 是否在同一产业链节点中去除重复公司，这是考虑到可能涉及到多篇研同一产业链节点的溯源
    thread_num: int = 32,  # 多线程数量
    is_use_cache: bool = False
) -> SuccessResponse | FalseResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request, thread_num=thread_num, is_use_cache=is_use_cache))

        mongodb_ids = request.mongodb_ids

        # 获取MongoDB集合名，为空则默认使用研报集合
        collection_names = request.collection_names
        if collection_names is None or len(collection_names) == 0:
            collection_names = [CollectionConfig.RESEARCH_REPORT_LABEL_INFO] * len(mongodb_ids)

        nodes_sources_dict = {}  # 从产业链节点溯源到 研报/资讯 的mongoid
        chain_structure_json = {}  # json 格式存单篇 研报/资讯 的产业链结构
        # 获取产业链结构，request.chain_structure为None则自动根据mongodb_ids中的 研报/资讯 生成产业链结构，并为每一个产业链节点溯源到相应mongodb_id
        if request.chain_structure is None:
            for i, (mongodb_id, collection_name) in enumerate(zip(request.mongodb_ids, request.collection_names)):
                LogUtil.info(f"正在从文档提取产业链节点 {i + 1}/{len(request.mongodb_ids)}")

                # 跳过非 研报/资讯 的文档
                if collection_name not in [CollectionConfig.RESEARCH_REPORT_LABEL_INFO,
                                           CollectionConfig.NEWS_LABEL_INFO]:
                    continue

                # 跳过非当前产业的 研报/资讯
                try:
                    query_res = MongodbUtil.query_doc_by_id(collection_name, mongodb_id)
                    report_industry_name = query_res["file"]["file_flag"]["产业类型"]
                    report_industry_name_list = report_industry_name.split("，")
                except Exception as e:
                    LogUtil.error("search_key_companies_compose中产业类型查询异常，已跳过该篇文档：" + str(
                        e) + f"\nmongodb_id: {mongodb_id}")
                    continue
                if request.industry not in report_industry_name_list:
                    continue

                chain_structure_res = await asyncio.gather(research_report_structure(
                    ResearchReportStructureRequest(
                        model=request.model,
                        collection_name=collection_name,
                        mongodb_id=mongodb_id,
                        k=request.k,
                        industry=request.industry,
                        system_prompt=PromptConfig.RESEARCH_REPORT_STRUCTURE_DIRECT_SYSTEM_PROMPT
                    ),
                    is_use_cache=is_use_cache
                ))
                chain_structure_res_json_str = chain_structure_res[0].data["result"]
                try:
                    chain_structure_res_json_str = TextUtil.get_json_from_text(chain_structure_res_json_str).group()
                    chain_structure_res_json = json.loads(chain_structure_res_json_str)
                    chain_structure_json[mongodb_id] = chain_structure_res_json
                except Exception as e:
                    LogUtil.error("search_key_companies_compose中产业链结构json解析异常，已跳过该篇文档：" + str(
                        e) + f"\nmongodb_id: {mongodb_id}, chain_structure_res_json_str: {chain_structure_res_json_str}")
                    continue
                chain_structure_nodes = TreeUtils.extract_nodes(chain_structure_res_json)
                for chain_structure_node in chain_structure_nodes:
                    if nodes_sources_dict.get(chain_structure_node) is None:
                        nodes_sources_dict[chain_structure_node] = {}
                    if nodes_sources_dict[chain_structure_node].get("source_id_list") is None:
                        nodes_sources_dict[chain_structure_node]["source_id_list"] = []
                    nodes_sources_dict[chain_structure_node]["source_id_list"].append(mongodb_id)
        else:
            chain_structure_nodes = TreeUtils.extract_nodes(request.chain_structure)
            for chain_structure_node in chain_structure_nodes:
                # 如果产业链结构是直接函数传参传进来的，则无法溯源
                nodes_sources_dict[chain_structure_node] = {"source_id_list": []}

        # 调用递归函数得到以'|'连接的节点名称列表
        # nodes = TreeUtils.extract_nodes(request.chain_structure)
        # 节点企业字典
        node_companies = {}
        tasks = []
        tasks_nodes = []
        task_mongodb_ids = []  # 溯源
        mongdb_id_companies = {}  # 溯源
        mongodb_id_collection_names = {}  # 溯源，该mongodb_id所在集合，现在不只用到研报这一个集合了 # TODO 未使用

        for mongodb_id, collection_name in zip(mongodb_ids, collection_names):
            mongdb_id_companies[mongodb_id] = []
            mongodb_id_collection_names[mongodb_id] = collection_name  # 溯源集合名

        for node in nodes_sources_dict:
            node_companies[node] = []
            if len(node.split("|")) > 2:  # 产业链和上中下游不查询相关企业
                link = node.split("|")[-1]
                search_key_companies_system_prompt = PromptConfig.SEARCH_KEY_COMPNIES_SYSTEM_PROMPT.format(
                    industry=request.industry, link=link)
                # 创建并发执行的任务列表，遍历研报重复epoch次得到结果
                task = [search_key_companies(
                    SearchKeyCompaniesRequest(
                        collection_name=collection_name,
                        mongodb_id=mongodb_id,
                        model=request.model,
                        k=request.k,
                        industry=request.industry,
                        system_prompt=search_key_companies_system_prompt,
                        link=link
                    ),
                    is_use_cache=is_use_cache
                ) for mongodb_id, collection_name in zip(mongodb_ids, collection_names) for _ in
                    range(request.epochs)]  # 重复epochs次
                tasks.extend(task)
                tasks_nodes.extend([node for _ in mongodb_ids for _ in range(request.epochs)])
                task_mongodb_ids.extend([mongodb_id for mongodb_id in mongodb_ids for _ in range(request.epochs)])

        for i in range(0, len(tasks), thread_num):
            end = min(i + thread_num, len(tasks))
            # 并发执行任务
            results = await asyncio.gather(*tasks[i:end])
            try:
                # 处理结果
                for single_report_info, node, mongodb_id in zip(results, tasks_nodes[i:end], task_mongodb_ids[i:end]):
                    if single_report_info.code == 200:
                        single_report_result = TextUtil.remove_think(single_report_info.data.get("result", ""))
                        single_report_result_json_match = TextUtil.get_json_from_text(single_report_result)
                        if single_report_result_json_match:
                            try:
                                single_report_result_json_str = single_report_result_json_match.group()

                                # # json字符串规范化 # TODO 大模型json格式错误种类太多了，不能使用TextUtil.get_json_from_text()，需要重写json匹配逻辑，
                                # single_report_result_json_str = single_report_result_json_str.replace("\n", "")
                                # single_report_result_json_str = single_report_result_json_str.strip(
                                #     "{}")  # 防止出现类似这种情况：{...}}
                                # single_report_result_json_str = "{" + single_report_result_json_str + "}"

                                result = json.loads(single_report_result_json_str)
                                for company in result.get("companies", []):
                                    if remove_duplicates_company_in_same_node:  # 检查是否已启用去重功能
                                        if "（" in company and company.endswith("）"):  # 去掉括号内的内容 芯动联科（688582）
                                            company = company.split("（")[0].strip()
                                        if "|" in company:  # 包含竖杆 | 内容的公司需要匹配竖杆 | 后面的简称 SF | 顺丰速运
                                            company = company.split("|")[-1].strip()

                                        if company.lower() not in "|".join(node_companies[node]).lower():  # 转换为小写并去重
                                            node_companies[node].append(company)
                                            # 溯源
                                            if company not in mongdb_id_companies[mongodb_id]:
                                                mongdb_id_companies[mongodb_id].append(company)
                                    else:
                                        node_companies[node].append(company)
                                        # 溯源
                                        if company not in mongdb_id_companies[mongodb_id]:
                                            mongdb_id_companies[mongodb_id].append(company)
                            except Exception as e:
                                LogUtil.error("search_key_companies_compose中json解析异常：" + str(
                                    e) + f"\nmongodb_id: {mongodb_id}, single_report_result: {single_report_result}")
                                continue
            except Exception as e:
                LogUtil.error("search_key_companies_compose中调用search_key_companies服务异常：" + str(e))
                continue

        # 逐层级合并企业名称列表
        node_companies = TreeUtils.merge_data(node_companies)
        data = {
            "result": node_companies,
            "mongdb_id_companies": mongdb_id_companies,
            "nodes_sources_dict": nodes_sources_dict,
            "chain_structure_json": chain_structure_json
        }
        # 记录返回日志
        LogUtil.log_json(describe="多研报中心客群组合结果请求返回结果", kwargs=data)
        return SuccessResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)
