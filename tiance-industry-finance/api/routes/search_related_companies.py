#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :search_related_companies.py
@Description  :
<AUTHOR>
@Date         :2025/03/03 11:58:23
'''

from fastapi import APIRouter
from entity.request_entity import SearchRelatedCompaniesRequest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from utils.mongodb_util import MongodbUtil
from utils.time_util import TimeUtil
from utils.mongodb_util import MongodbUtil
from configs.collection_config import CollectionConfig
import re

async def get_related_companies_details(company, collection_name, related_companies, related_company_relations, update_time):
    records = list(MongodbUtil.coll(collection_name).find({"company_name": company["abb"]}))
    if len(records) == 0:
        print(f'{company["abb"]}--没有查询到关联企业')
        pass
    else:
        for record in records:
            # 溯源
            source_info = MongodbUtil.coll(CollectionConfig.NOTICE).find_one({"_id": record["source_id"]}, {"data", "file"})
            if source_info:
                source_info.pop("_id")
                source_info["source_title"] = source_info["file"]["file_title"]
                source_info["source_name"] = source_info["data"]["data_source"]
                source_info["source_type"] = source_info["data"]["data_type"]
                source_info["source_url"] = source_info["file"]["file_url"]
                # 删除_id
                source_info.pop("data")
                source_info.pop("file")
            else:
                source_info = {"source_title": "", "source_name": "", "source_type": "", "source_url": ""}
            for relation_info in record["待入库具体信息"]:
                # 关联企业名称加关系作为主键
                relation_primary = relation_info["affiliate"] + relation_info["relation_type"]
                if relation_info["affiliate"] and relation_primary not in related_company_relations:
                    listed_company = MongodbUtil.coll(CollectionConfig.LISTED_COMPANIES_INFO).find_one({"证券简称": relation_info["affiliate"]})
                    related_company = {
                        "abb": relation_info["affiliate"],
                        "name": relation_info["affiliate"],
                        "stream_type": company.get("stream_type",''),
                        "is_listed": "",
                        "industry_position": "",
                        "key_company": company["name"],
                        "related_type": relation_info["relation_type"],
                        "update_time": update_time,
                        "credit_code": "",
                        "registered_capital": "",
                        "main_products": "",
                        "company_web": "",
                        "company_attributes": "",
                        "company_profile": "",
                        "business_scope": "",
                        "city": "",
                        "registered_address": "",
                        "is_special": "",
                        "is_high_tech": "",
                        "is_sub_special": ""
                    }
                    if listed_company:
                        related_company["is_listed"] = "是"
                        related_company["name"] = listed_company["公司中文名称"]
                        related_company["credit_code"] = listed_company["统一社会信用代码"]
                        related_company["registered_capital"] = listed_company["注册资本\n[单位] 元"]
                        related_company["main_products"] = listed_company["主营产品名称"]
                        related_company["company_web"] = listed_company["公司网站"]
                        related_company["company_attributes"] = listed_company["公司属性\n[交易日期] 最新收盘日"]
                        related_company["company_profile"] = listed_company["公司简介↓"]
                        related_company["business_scope"] = listed_company["经营范围"]
                        related_company["city"] = listed_company["城市"]
                        related_company["registered_address"] = listed_company["注册地址"]
                        related_company["is_special"] = "是" if listed_company["是否企业本身为专精特新"] == 1 else "否"
                        related_company["is_high_tech"] = "是" if listed_company["是否高新技术企业"] == 1 else "否"
                        related_company["is_sub_special"] = "是" if listed_company["是否企业的参控股公司为专精特新企业"] == 1 else "否"
                    else:
                        listed_company_by_name = MongodbUtil.coll(CollectionConfig.LISTED_COMPANIES_INFO).find_one({"公司中文名称": relation_info["affiliate"]})
                        if listed_company_by_name:
                            related_company["is_listed"] = "是"
                            related_company["abb"] = listed_company_by_name["证券简称"]

                    # 中英文括号处理
                    en_bracket_name = related_company["name"].replace("（","(").replace("）",")")
                    zh_bracket_name = related_company["name"].replace("(","（").replace(")","）")
                    basic_info = MongodbUtil.coll(CollectionConfig.BASIC_INFO).find_one({"$or": [{"公司名称": related_company["name"]},
                                                                                                 {"曾用名": {"$regex": re.escape(related_company["name"])}},
                                                                                                 {"公司名称": en_bracket_name},
                                                                                                 {"曾用名": {"$regex": re.escape(en_bracket_name)}},
                                                                                                 {"公司名称": zh_bracket_name},
                                                                                                 {"曾用名": {"$regex": re.escape(zh_bracket_name)}}
                                                                                                 ]})
                    related_company["stock_code"] = "-"
                    related_company["province"] = "-"
                    related_company["city"] = "-"
                    if basic_info:
                        related_company["stock_code"] = basic_info.get("股票代码") if isinstance(basic_info.get("股票代码"), str) else "-"
                        related_company["province"] = basic_info.get("所属省") if isinstance(basic_info.get("所属省"), str) else "-"
                        related_company["city"] = basic_info.get("所属市") if isinstance(basic_info.get("所属市"), str) else "-"
                        related_company["is_listed"] = basic_info.get("是否上市") if isinstance(basic_info.get("所属市"), str) else ""

                    industry_position_info = MongodbUtil.coll(CollectionConfig.INDUSTRY_POSITION_INFO).find_one({"企业名称": related_company["name"]})
                    related_company["industry_position"] = ""
                    if industry_position_info:
                        related_company["industry_position"] = industry_position_info.get("产业地位")
                    related_company["source_list"] = [source_info]
                    related_company_relations.append(relation_primary)
                    related_companies.append(related_company)
        print(f'{company["abb"]}--查询到关联企业成功')

router = APIRouter()
@router.post("/search_related_companies", summary="根据中心客群查询关联企业")
async def search_related_companies(request: SearchRelatedCompaniesRequest) -> SuccessResponse | FalseResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request))
        key_companies = request.key_companies
        related_companies = []
        related_company_relations = []
        update_time = TimeUtil.get_current_format_time()
        for company in key_companies:
            if company["is_listed"] == "是":
                # 查询招股说明书中的关联企业
                await get_related_companies_details(company, CollectionConfig.IPO_INFO, related_companies, related_company_relations, update_time)
                # 查询年报中的关联企业   
                await get_related_companies_details(company, CollectionConfig.ANNUAL_REPORT_INFO, related_companies, related_company_relations, update_time)
        data = {"result": related_companies}
        # 记录返回日志
        LogUtil.log_json(describe="根据中心客群查询关联企业请求返回结果", kwargs=data)
        return SuccessResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)
