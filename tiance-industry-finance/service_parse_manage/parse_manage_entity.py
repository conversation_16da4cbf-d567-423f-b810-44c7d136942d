#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 12:30:00
# <AUTHOR> Assistant
# @File         : data_manage_entity.py
# @Description  : 数据管理实体类
"""
from fastapi import UploadFile, File, Body
from pydantic import BaseModel, Field
from typing import List, Optional, Literal
from datetime import datetime
from typing import Optional
from enum import Enum

class CompanyDataStatsRequest(BaseModel):
    """公司数据总量查询请求实体"""
    pass  # 无需参数，查询所有统计数据


class CompanyUpdateRequest(BaseModel):
    """公司数据修改请求实体"""
    company_code: str = Field(..., example="COMP001", description="企业编号（必填）")
    chi_name: str = Field(..., example="新公司名称", description="中文名称（必填）")
    chi_name_abbr: str = Field(None, example="新公司简称", description="企业别称（可选）")
    eng_name: str = Field(None, example="New Company Name", description="英文全称（可选）")


class CompanyBatchUpdateRequest(BaseModel):
    """公司数据批量修改请求实体"""
    companies: List[CompanyUpdateRequest] = Field(..., description="公司修改数据列表")


class CompanyDataDownloadRequest(BaseModel):
    """公司数据下载实体"""
    company_code_list: List[str] = Field(..., example=["COMP001", "COMP002"], description="企业编号（必填）")



class CompanyAddRequest(BaseModel):
    """公司信息新增主实体"""
    ChiName: str = Field(...,example="阿里巴巴集团",description="企业中文全称（必填项）")
    ChiNameAbbr: Optional[str] = Field(None,example="阿里集团",description="企业中文简称（可选）")
    PreName: Optional[str] = Field(None,example="阿里巴巴网络技术有限公司",description="曾用名称（可选）")
    EngName: Optional[str] = Field(None,example="Alibaba Group",description="企业英文全称（可选）")
    EngNameAbbr: Optional[str] = Field(None,example="Alibaba",description="企业英文简称（可选）")
    StockAbbr: Optional[str] = Field(None,example="阿里巴巴",description="股票简称（可选）")
    BrandName: Optional[str] = Field(None,example="淘宝",description="品牌名称（可选）")
    LargeModelAbbr: Optional[str] = Field(None,example="大模型公司",description="大类模型简称（可选）")
    OtherAbbr: Optional[str] = Field(None,example="阿里",description="其他简称（可选）")
    CreditCode: str = Field(...,example="91330100716105852F",description="统一社会信用代码（必填项）")

class CompanyBatchAddRequest(BaseModel):
    """公司信息批量新增请求实体"""
    company_list: List[CompanyAddRequest] = Field(...,description="公司信息列表（支持批量新增）" )

class CompanyDelRequest(BaseModel):
    """公司信息删除主实体"""
    CompanyCode: str = Field(..., max_length=100, description="企业代码")
    ChiName: str = Field(...,example="阿里巴巴集团",description="企业中文全称（必填项）")
    ChiNameAbbr: Optional[str] = Field(None,example="阿里集团",description="企业中文简称（可选）")
    PreName: Optional[str] = Field(None,example="阿里巴巴网络技术有限公司",description="曾用名称（可选）")
    EngName: Optional[str] = Field(None,example="Alibaba Group",description="企业英文全称（可选）")
    StockAbbr: Optional[str] = Field(None,example="阿里巴巴",description="股票简称（可选）")
    CreditCode: Optional[str] = Field(None,example="91330100716105852F",description="统一社会信用代码（可选）")

class CompanyBatchDelRequest(BaseModel):
    """公司信息批量删除请求实体"""
    company_list: List[CompanyDelRequest] = Field(...,description="公司信息列表（支持批量删除）" )


class CompanyMainResponse(BaseModel):
    """企业主信息表 ORM 模型"""
    
    CompanyCode: str = Field(..., max_length=100, description="企业代码", alias="CompanyCode")
    ChiName: str = Field(..., max_length=100, description="中文名称", alias="ChiName")
    ChiNameAbbr: Optional[str] = Field(None, max_length=100, description="企业别称", alias="ChiNameAbbr")
    PreName: Optional[str] = Field(None, description="曾用名", alias="PreName")
    EngName: Optional[str] = Field(None, max_length=200, description="英文全称", alias="EngName")
    EngNameAbbr: Optional[str] = Field(None, max_length=100, description="英文简称", alias="EngNameAbbr")
    StockAbbr: Optional[str] = Field(None, max_length=100, description="股票简称", alias="StockAbbr")
    BrandName: Optional[str] = Field(None, max_length=500, description="品牌名称", alias="BrandName")
    LargeModelAbbr: Optional[str] = Field(None, max_length=100, description="大模型简称", alias="LargeModelAbbr")
    OtherAbbr: Optional[str] = Field(None, max_length=100, description="其他简称", alias="OtherAbbr")
    CreditCode: Optional[str] = Field(None, max_length=255, description="信用代码", alias="CreditCode")
    create_time: datetime = Field(default_factory=datetime.now, description="创建时间", alias="create_time")
    update_time: datetime = Field(default_factory=datetime.now, description="更新时间", alias="update_time")
    status: int = Field(default=1, ge=0, le=1, description="状态：0-删除，1-正常", alias="status")

    class Config:
        from_attributes = True

class CompanyMainRequest(BaseModel):
    """公司数据查询请求实体"""
    page_num: int = Field(default=1,gt=0,description="页码（从1开始）")
    page_size: int = Field(default=10,gt=0,description="每页数量（最大100）")
    company_name_info: Optional[str] = Field(default=None,description="企业名称模糊搜索）")


class Document_DataType(str, Enum):
    """文档数据类型枚举"""
    RESEARCH = "研报"
    NOTICE = "公告"
    INVOICE = "发票"
    CUSTOMS = "海关"
    POLICY = "政策"
    NEWS = "资讯"

# 数据类型到集合名称的映射
COLLECTION_MAPPING = {
    Document_DataType.RESEARCH: "research_label_info",
    Document_DataType.NOTICE: "notice_label_info_newx",
    Document_DataType.INVOICE: "invoice_label_info_newx",
    Document_DataType.CUSTOMS: "customs_label_info_newx",
    Document_DataType.POLICY: "policy_label_info_newx",
    Document_DataType.NEWS: "news_label_info_newx"
}

# 数据类型到中文描述的映射
DATA_TYPE_LABELS = {
    Document_DataType.RESEARCH: "研报",
    Document_DataType.NOTICE: "公告",
    Document_DataType.INVOICE: "发票",
    Document_DataType.CUSTOMS: "海关",
    Document_DataType.POLICY: "政策",
    Document_DataType.NEWS: "资讯"
}

class UpdateDocumentRequest(BaseModel):
    """允许更新的字段模型"""
    collection_type: Optional[str] = Field(None, description="collection_type")
    document_id: Optional[str] = Field(None, max_length=255, description="文件mongoid")

    file_title: Optional[str] = Field(None, max_length=255, description="文件TITLE")
    file_type: Optional[str] = Field(None, max_length=255, description="文件TYPE")
    file_url: Optional[str] = Field(None, max_length=500, description="文件URL")
    file_flag: Optional[dict] = Field(None, max_length=500, description="文件FLAG")

class DelDocumentRequest(BaseModel):
    """软删除mongo"""
    collection_type: Optional[str] = Field(None, description="文档数据类型")
    document_id: Optional[str] = Field(None, max_length=255, description="文件mongoid"),



class DocumentParseDictEntity():
    # mongo映射字典
    MONGO_DATASET_TYPE_DICT = {
        0: "research_label_info",
        1: "notice_label_info_new",
        2: "invoice_label_info_new",
        3: "customs_label_info_new",
        4: "policy_label_info_new",
        5: "news_label_info_new"
    }
    # 数据类型映射字典
    DATA_TYPE_DICT = {
        0: "研报",
        1: "公告",
        2: "发票",
        3: "海关",
        4: "政策",
        5: "资讯"
    }
    # minio映射字典
    MINIO_TYPE_DICT = {
        0: "research",
        1: "notice",
        2: "invoice",
        3: "customs",
        4: "policy",
        5: "news"
    }

    FILE_CACHE_FOLDER = r"C:\Users\<USER>\Desktop\new_project_code\tiance-industry-finance\file"



class QueryDocumentRequest(BaseModel):
    """mongo数据查讯请求实体"""
    collection_type: Literal["news", "policy", "notice", "customs", "invoice", "research"] = Field(
        ...,
        example="notice",
        description="文档类型（必填），可选值：news(新闻)、policy(政策)、notice(通知)、customs(海关)、invoice(发票)、research(调研)"
    )
    file_title: str = Field(
        ...,
        example="中直股份",
        max_length=200,
        description="文件标题（必填），最大长度200字符"
    )
    page: int = Field(
        ...,
        example=1,
        description="查看结果第几页（选填），默认第一页"
    )
