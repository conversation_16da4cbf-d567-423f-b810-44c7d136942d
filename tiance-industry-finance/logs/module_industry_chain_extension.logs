[2025-07-16 15:46:38,765] [module_industry_chain_extension 54975] [webpage_download_service.create_download_job: 111] ERROR: 创建网页下载任务失败: type object 'UuidUtil' has no attribute 'generate_uuid'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance/service_crawer_manage/service/webpage_download_service.py", line 43, in create_download_job
    job_id = UuidUtil.generate_uuid()
AttributeError: type object 'UuidUtil' has no attribute 'generate_uuid'
[2025-07-16 15:59:19,353] [module_industry_chain_extension 99219] [webpage_download_service._start_background_download: 132] INFO: 后台下载任务已启动: job_id=9cc00d9b50a34ed884eb201af37b19a4
[2025-07-16 15:59:19,354] [module_industry_chain_extension 99219] [webpage_download_service.create_download_job: 100] INFO: 网页下载任务创建成功: job_id=9cc00d9b50a34ed884eb201af37b19a4, 总数量=1
[2025-07-16 15:59:20,578] [module_industry_chain_extension 99219] [webpage_download_service._execute_download_job: 236] ERROR: 执行下载任务失败: job_id=9cc00d9b50a34ed884eb201af37b19a4, error=No module named 'pyppeteer'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance/service_crawer_manage/service/webpage_download_service.py", line 164, in _execute_download_job
    from script.download_pdfs import download_and_convert_to_pdf_pyppeteer
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance/script/download_pdfs.py", line 2, in <module>
    from pyppeteer import launch
ModuleNotFoundError: No module named 'pyppeteer'
[2025-07-16 16:08:15,603] [module_industry_chain_extension 8142] [webpage_query_service.query_job_status: 67] INFO: 查询任务状态成功: job_id=9cc00d9b50a34ed884eb201af37b19a4
[2025-07-16 16:08:33,033] [module_industry_chain_extension 8142] [webpage_query_service.query_job_details: 131] INFO: 查询任务详情成功: job_id=9cc00d9b50a34ed884eb201af37b19a4, page=1, page_size=20
[2025-07-16 16:09:19,028] [module_industry_chain_extension 8142] [webpage_query_service.query_jobs_list: 194] INFO: 查询任务列表成功: page=1, page_size=20, job_status=None
[2025-07-16 16:09:28,628] [module_industry_chain_extension 8142] [webpage_query_service.query_jobs_list: 194] INFO: 查询任务列表成功: page=1, page_size=20, job_status=FAILED
[2025-07-16 16:09:35,322] [module_industry_chain_extension 8142] [webpage_query_service.query_jobs_list: 194] INFO: 查询任务列表成功: page=1, page_size=20, job_status=SUCCESS
[2025-07-16 16:18:20,284] [module_industry_chain_extension 8142] [webpage_retry_service._start_background_retry: 122] INFO: 后台重试任务已启动，共 1 个网页
[2025-07-16 16:18:20,284] [module_industry_chain_extension 8142] [webpage_retry_service.retry_failed_downloads: 91] INFO: 重新下载任务启动成功，共 1 个网页待重试
[2025-07-16 16:18:20,317] [module_industry_chain_extension 8142] [webpage_retry_service._execute_retry_downloads: 202] ERROR: 执行重试下载任务失败: error=Instance <WebpageDownloadDetail at 0x12fc76ce0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance/service_crawer_manage/service/webpage_retry_service.py", line 142, in _execute_retry_downloads
    SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/attributes.py", line 566, in __get__
    return self.impl.get(state, dict_)  # type: ignore[no-any-return]
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/attributes.py", line 1086, in get
    value = self._fire_loader_callables(state, key, passive)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/attributes.py", line 1116, in _fire_loader_callables
    return state._load_expired(state, passive)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/state.py", line 803, in _load_expired
    self.manager.expired_attribute_loader(self, toload, passive)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/loading.py", line 1603, in load_scalar_attributes
    raise orm_exc.DetachedInstanceError(
sqlalchemy.orm.exc.DetachedInstanceError: Instance <WebpageDownloadDetail at 0x12fc76ce0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance/service_crawer_manage/service/webpage_retry_service.py", line 189, in _execute_retry_downloads
    SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/attributes.py", line 566, in __get__
    return self.impl.get(state, dict_)  # type: ignore[no-any-return]
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/attributes.py", line 1086, in get
    value = self._fire_loader_callables(state, key, passive)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/attributes.py", line 1116, in _fire_loader_callables
    return state._load_expired(state, passive)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/state.py", line 803, in _load_expired
    self.manager.expired_attribute_loader(self, toload, passive)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/loading.py", line 1603, in load_scalar_attributes
    raise orm_exc.DetachedInstanceError(
sqlalchemy.orm.exc.DetachedInstanceError: Instance <WebpageDownloadDetail at 0x12fc76ce0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
[2025-07-16 16:18:43,870] [module_industry_chain_extension 8142] [webpage_query_service.query_jobs_list: 194] INFO: 查询任务列表成功: page=1, page_size=20, job_status=SUCCESS
[2025-07-16 16:18:51,386] [module_industry_chain_extension 8142] [webpage_query_service.query_job_details: 131] INFO: 查询任务详情成功: job_id=9cc00d9b50a34ed884eb201af37b19a4, page=1, page_size=20
[2025-07-16 16:19:54,645] [module_industry_chain_extension 8142] [webpage_query_service.query_job_details: 131] INFO: 查询任务详情成功: job_id=9cc00d9b50a34ed884eb201af37b19a4, page=1, page_size=20
[2025-07-16 16:28:47,188] [module_industry_chain_extension 8142] [webpage_query_service.query_job_details: 131] INFO: 查询任务详情成功: job_id=9cc00d9b50a34ed884eb201af37b19a4, page=1, page_size=20
[2025-07-16 16:29:52,215] [module_industry_chain_extension 8142] [webpage_query_service.query_job_details: 131] INFO: 查询任务详情成功: job_id=9cc00d9b50a34ed884eb201af37b19a4, page=1, page_size=20
[2025-07-16 16:30:30,476] [module_industry_chain_extension 8142] [webpage_retry_service._start_background_retry: 122] INFO: 后台重试任务已启动，共 1 个网页
[2025-07-16 16:30:30,479] [module_industry_chain_extension 8142] [webpage_retry_service.retry_failed_downloads: 91] INFO: 重新下载任务启动成功，共 1 个网页待重试
[2025-07-16 16:30:30,527] [module_industry_chain_extension 8142] [webpage_retry_service._execute_retry_downloads: 202] ERROR: 执行重试下载任务失败: error=Instance <WebpageDownloadDetail at 0x12ff4ed70> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
Traceback (most recent call last):
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/default.py", line 941, in do_execute
    cursor.execute(statement, parameters)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/cursors.py", line 153, in execute
    result = self._query(query)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/cursors.py", line 322, in _query
    conn.query(q)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 563, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 825, in _read_query_result
    result.read()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 1206, in read
    self._read_result_packet(first_packet)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 1282, in _read_result_packet
    self._get_descriptions()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 1367, in _get_descriptions
    field = self.connection._read_packet(FieldDescriptorPacket)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 771, in _read_packet
    packet = packet_type(bytes(buff), self.encoding)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/protocol.py", line 234, in __init__
    self._parse_field_descriptor(encoding)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/protocol.py", line 253, in _parse_field_descriptor
    ) = self.read_struct("<xHIBHBxx")
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/protocol.py", line 177, in read_struct
    result = s.unpack_from(self._data, self._position)
struct.error: unpack_from requires a buffer of at least 146 bytes for unpacking 13 bytes at offset 133 (actual buffer size is 137)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1127, in _rollback_impl
    self.engine.dialect.do_rollback(self.connection)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/default.py", line 699, in do_rollback
    dbapi_connection.rollback()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 492, in rollback
    self._execute_command(COMMAND.COM_QUERY, "ROLLBACK")
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 843, in _execute_command
    raise err.InterfaceError(0, "")
pymysql.err.InterfaceError: (0, '')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance/service_crawer_manage/service/webpage_retry_service.py", line 142, in _execute_retry_downloads
    SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/attributes.py", line 566, in __get__
    return self.impl.get(state, dict_)  # type: ignore[no-any-return]
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/attributes.py", line 1086, in get
    value = self._fire_loader_callables(state, key, passive)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/attributes.py", line 1116, in _fire_loader_callables
    return state._load_expired(state, passive)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/state.py", line 803, in _load_expired
    self.manager.expired_attribute_loader(self, toload, passive)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/loading.py", line 1670, in load_scalar_attributes
    result = load_on_ident(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/loading.py", line 509, in load_on_ident
    return load_on_pk_identity(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/loading.py", line 694, in load_on_pk_identity
    session.execute(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 2362, in execute
    return self._execute_internal(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 2247, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/context.py", line 305, in orm_execute_statement
    result = conn.execute(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1418, in execute
    return meth(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/sql/elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1640, in _execute_clauseelement
    ret = self._execute_context(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1846, in _execute_context
    return self._exec_single_context(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 2349, in _handle_dbapi_exception
    self._rollback_impl()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1129, in _rollback_impl
    self._handle_dbapi_exception(e, None, None, None, None)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 2257, in _handle_dbapi_exception
    raise exc.DBAPIError.instance(
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1127, in _rollback_impl
    self.engine.dialect.do_rollback(self.connection)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/engine/default.py", line 699, in do_rollback
    dbapi_connection.rollback()
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 492, in rollback
    self._execute_command(COMMAND.COM_QUERY, "ROLLBACK")
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/pymysql/connections.py", line 843, in _execute_command
    raise err.InterfaceError(0, "")
sqlalchemy.exc.InterfaceError: (pymysql.err.InterfaceError) (0, '')
(Background on this error at: https://sqlalche.me/e/20/rvf5)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/GitHub/tiance-industry-finance/service_crawer_manage/service/webpage_retry_service.py", line 189, in _execute_retry_downloads
    SQLUtil.update_by_id(WebpageDownloadDetail, detail.id, {
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/attributes.py", line 566, in __get__
    return self.impl.get(state, dict_)  # type: ignore[no-any-return]
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/attributes.py", line 1086, in get
    value = self._fire_loader_callables(state, key, passive)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/attributes.py", line 1116, in _fire_loader_callables
    return state._load_expired(state, passive)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/state.py", line 803, in _load_expired
    self.manager.expired_attribute_loader(self, toload, passive)
  File "/opt/anaconda3/envs/tiance-industry-finance/lib/python3.10/site-packages/sqlalchemy/orm/loading.py", line 1603, in load_scalar_attributes
    raise orm_exc.DetachedInstanceError(
sqlalchemy.orm.exc.DetachedInstanceError: Instance <WebpageDownloadDetail at 0x12ff4ed70> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
