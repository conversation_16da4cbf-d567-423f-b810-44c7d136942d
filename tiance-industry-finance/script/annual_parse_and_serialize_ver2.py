#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@Time    :   2025/01/16 11:42:55
<AUTHOR>   WEIHA<PERSON> HONG 
@Email    :   <EMAIL>
@File    :   annual_parse_and_serialize.py
@Project    :   tiance-industry-finance
'''
from configs.collection_config import CollectionConfig
from utils.milvus_util import MilvusUtil
from utils.mongodb_util import MongodbUtil
import re
import requests
from service.llm_extract_name_service import LlmHelper

if __name__ == '__main__':

        
    url = "http://10.8.21.163:19029/annual_report_info_ext_with_file"
    company_name="测试公司"
    file_name="测试文档"
    year = "1970"
    industry_chain = "数据重跑2"
    MongodbUtil.connect()
    docs = MongodbUtil.query_all_doc(CollectionConfig.NOTICE_ALL)
    for doc in docs:
        if doc["data"]["data_type"] != "年报":
            print("data_type",doc["file"]["file_title"])
            continue
        if "摘要" in doc["file"]["file_title"] :
            print("filter",doc["file"]["file_title"])
            continue
        if doc["milvus"]["milvus_collection_name"] == "None" :
            print("empty milvus",doc["file"]["file_title"])
            continue
        if doc["milvus"]["milvus_collection_name"] == "notice_vector_library" :
            
            print("doc",doc)
            response = requests.post(url, json={"company_name": company_name,"pdf_filename":file_name,"year":year,"industry_chain":industry_chain,"mongodb_id":doc["_id"]})
            print(response.json())
        else:
            print("unprocess")

    # print("doc")
    # response = requests.post(url, json={"company_name": company_name,"pdf_filename":file_name,"year":year,"industry_chain":industry_chain,"mongodb_id":"7775e8ab7b81fa135717e56acf9fd94f"})
    # print(response.json())
    