import os
import json
from pymongo.errors import Duplicate<PERSON>eyError
from datetime import datetime  # Import datetime module

# Import actual utility classes
# Ensure minio_util.py and mongodb_util.py are accessible in the current script's path
from utils.minio_util import MinIoUtil
from utils.mongodb_util import MongodbUtil


# MinIO and MongoDB configuration information should be handled internally by the imported
# minio_util and mongodb_util files. We no longer define MinioConfig and MongodbConfig classes here.
file_to_exclude = ["976151deb216765a830d062a1d656b2a"]
def process_data(downloaded_pdfs_path, generated_jsons_path):
    """
    Processes JSON files, inserts/updates them in MongoDB, and uploads/updates related PDFs in MinIO.

    Args:
        downloaded_pdfs_path (str): The path to the directory containing downloaded PDF files.
        generated_jsons_path (str): The path to the directory containing generated JSON files.
    """
    # Connect to MongoDB and MinIO
    # Call the connect methods of <PERSON><PERSON>o<PERSON><PERSON> and <PERSON>godb<PERSON>til to establish connections
    MinIoUtil.connect()
    MongodbUtil.connect()

    policy_types = ["政策", "政策资讯", "政策解读", "产业政策"]
    news_types = ["资讯"]

    # Ensure the error log directory exists
    os.makedirs("error_logs", exist_ok=True)

    # Generate a log file name with the current run time
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    error_log_path = f"error_logs/processing_errors_{current_time}.txt"

    with open(error_log_path, 'w', encoding='utf-8') as error_file:
        # Iterate through all JSON files in the generated_jsons path
        for json_file_name in os.listdir(generated_jsons_path):
            if not json_file_name.endswith(".json"):
                continue

            json_file_path = os.path.join(generated_jsons_path, json_file_name)
            try:
                with open(json_file_path, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                collection_name = json_data.get("mongodb_collection_name")
                generated_json = json_data.get("generated_json")
                doc_id = generated_json.get('_id')  # Get the document's _id for duplication check
                
                # Process MinIO file uploads/updates
                minio_info = generated_json.get('minio', {})
                bucket_name = minio_info.get('minio_name')
                minio_document_paths = minio_info.get('minio_document_path', [])
                if bucket_name and minio_document_paths:
                    # No need to create bucket, as per user's request, buckets are assumed to exist.
                    print(f"跳过 MinIO 存储桶 '{bucket_name}' 的创建，假设其已存在。")
                    for remote_path in minio_document_paths:
                        file_name = os.path.basename(remote_path)
                        local_file_path = os.path.join(downloaded_pdfs_path, file_name)
                        # Check if the local file exists
                        if not os.path.exists(local_file_path):
                            error_message = f"错误: 本地 PDF 文件 '{local_file_path}' 不存在，无法上传/更新到 MinIO。来自 JSON: '{json_file_name}'。\n"
                            error_file.write(error_message)
                            print(error_message.strip())
                            continue
                        # Check if the same object already exists in MinIO
                        try:
                            # Use MinIoUtil.get_file_list to check for existence; it returns a list of object names
                            existing_objects_in_bucket = MinIoUtil.get_file_list(bucket_name=bucket_name,
                                                                                 prefix=remote_path)
                            object_exists = remote_path in existing_objects_in_bucket
                            if object_exists:
                                print(object_exists)
                                # If object exists, we perform an update (overwrite)
                                # MinIoUtil.upload_file(bucket_name, remote_path, local_file_path)
                                print(
                                    f"已跳过文件(文件已存在) '{local_file_path}' 到 MinIO: {bucket_name}/{remote_path}")
                                continue
                            else:
                                # If object does not exist, perform an initial upload
                                MinIoUtil.upload_file(bucket_name, remote_path, local_file_path)
                                print(f"成功上传文件 '{local_file_path}' 到 MinIO: {bucket_name}/{remote_path}。")
                        except Exception as e:
                            error_message = f"错误: 上传/更新文件 '{local_file_path}' 到 MinIO 失败: {e}。来自 JSON: '{json_file_name}'。\n"
                            error_file.write(error_message)
                            print(error_message.strip())
                elif not (bucket_name and minio_document_paths):
                    error_message = f"警告: 文件 '{json_file_name}' 缺少 MinIO 配置信息 (minio_name 或 minio_document_path)。跳过 MinIO 处理。\n"
                    error_file.write(error_message)
                    print(error_message.strip())
                if doc_id in file_to_exclude:
                    error_message=f"has igonre id :{doc_id}"
                    print(error_message)
                    error_file.write(error_message)
                    continue
                if collection_name:
                    # Attempt to insert or update in MongoDB
                    try:
                        # Use MongodbUtil.query_doc_by_id for existence check
                        existing_doc = MongodbUtil.query_doc_by_id(collection_name, doc_id)
                        if existing_doc:
                            # If the document exists, update it
                            # Assumes MongodbUtil.update_one(collection_name, filter_query, update_document) exists
                            # You need to ensure this method is implemented in your mongodb_util.py
                            # MongodbUtil.update_one(collection_name, {"_id": doc_id}, {"$set": generated_json})
                            print(
                                f"跳过更新文件 '{json_file_name}' (ID: {doc_id}) 到 MongoDB 集合 '{collection_name}'。")
                        else:
                            # If the document does not exist, insert a new document
                            MongodbUtil.insert_one(collection_name, generated_json)
                            print(f"成功插入文件 '{json_file_name}' 到 MongoDB 集合 '{collection_name}'。")
                    except DuplicateKeyError:
                        # Keep this catch for race conditions or unexpected behavior, even with existence checks
                        error_message = f"警告: 文档 ID '{doc_id}' 已存在于集合 '{collection_name}' 中 (DuplicateKeyError), 跳过插入/更新文件 '{json_file_name}'。\n"
                        error_file.write(error_message)
                        print(error_message.strip())
                    except Exception as e:
                        error_message = f"错误: 插入/更新文件 '{json_file_name}' 到 MongoDB 失败: {e}\n"
                        error_file.write(error_message)
                        print(error_message.strip())

            except json.JSONDecodeError as e:
                error_message = f"错误: 解析 JSON 文件 '{json_file_path}' 失败: {e}\n"
                error_file.write(error_message)
                print(error_message.strip())
            except Exception as e:
                error_message = f"错误: 处理文件 '{json_file_path}' 时发生未知错误: {e}\n"
                error_file.write(error_message)
                print(error_message.strip())

                raise e

    print(f"\n处理完成。所有错误和警告信息已记录到 '{error_log_path}'。")


if __name__ == "__main__":
    # Please ensure that the 'downloaded_pdfs' and 'generated_jsons' directories exist,
    # and contain the PDF and JSON files you want to process.
    # In a real-world scenario, these directories and files should be generated by your upstream processes.
    # We are no longer creating mock files here, assuming they already exist.

    downloaded_pdfs_directory = "downloaded_pdfs"
    generated_jsons_directory = "generated_jsons"

    # If these directories do not exist, it is recommended to create them manually
    # or ensure they exist before calling this script.
    # os.makedirs(downloaded_pdfs_directory, exist_ok=True)
    # os.makedirs(generated_jsons_directory, exist_ok=True)

    # First run to process all files
    print("--- First processing of files ---")
    process_data(downloaded_pdfs_directory, generated_jsons_directory)

    # If you need to test the duplicate insertion/update logic, ensure there are already processed JSON files
    # in the generated_jsons_directory. For example, you can copy previously processed JSON files
    # to this directory and then run the process_data function again.
    print("\n--- Attempting to process again, testing duplicate insertion/update logic ---")
    process_data(downloaded_pdfs_directory, generated_jsons_directory)

    # The code for cleaning up mock files and directories has been removed,
    # as it is now assumed that files and directories are real.
    # If cleanup is required, please perform it manually or write a separate cleanup script.
