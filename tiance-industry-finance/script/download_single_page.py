#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 20:30:00
# <AUTHOR> Assistant
# @File         : download_single_page.py
# @Description  : 单页面下载脚本，避免多线程问题
"""

import argparse
import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def main():
    parser = argparse.ArgumentParser(description='下载单个网页为PDF')
    parser.add_argument('--name', required=True, help='网页名称')
    parser.add_argument('--url', required=True, help='网页URL')
    parser.add_argument('--dir', required=True, help='下载目录')
    args = parser.parse_args()
    
    try:
        from script.download_pdfs import download_and_convert_to_pdf_pyppeteer
        
        # 确保下载目录存在
        os.makedirs(args.dir, exist_ok=True)
        
        # 执行下载
        success = await download_and_convert_to_pdf_pyppeteer(args.name, args.url, args.dir)
        
        if success:
            print(f"下载成功: {args.name}")
            sys.exit(0)
        else:
            print(f"下载失败: {args.name}")
            sys.exit(1)
            
    except Exception as e:
        print(f"下载异常: {args.name}, 错误: {str(e)}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
