#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time    : 2024/10/21 15:31
# <AUTHOR> Tao<PERSON><PERSON>
# @Email   : <EMAIL>
# @File    : mongodb_util.py
# @Project : tiance-industry-finance
"""

from pymongo import MongoClient
from configs.mongodb_config import MongodbConfig


class MongodbUtil(object):
    """
    mongodb工具类：pymongo和mongodb版本应该对应，避免出现一些奇怪的问题
    """
    __db = None
    __client = None

    @staticmethod
    def connect():
        """
        连接mongodb
        :return:
        """
        MongodbUtil._get_connection()


    @staticmethod
    def connect_dev():
        """
        连接mongodb
        :return:
        """
        MongodbUtil._get_connection_dev()


    @staticmethod
    def reconnect():
        """
        重新连接mongodb
        :return:
        """
        MongodbUtil.close()
        MongodbUtil.connect()

    @staticmethod
    def close():
        """
        重新连接mongodb
        :return:
        """
        MongodbUtil.__client.close()
        MongodbUtil.__db = None

    @staticmethod
    def _get_connection():
        """
        获取mongodb连接信息
        :return:
        """
        if MongodbUtil.__db is None:
            # 开启连接保活功能（防止长时间不使用连接导致断开）
            MongodbUtil.__client = MongoClient(host=MongodbConfig.MONGODB_HOST, port=MongodbConfig.MONGODB_PORT,
                                               username=MongodbConfig.MONGODB_USER, password=MongodbConfig.MONGODB_PASS,
                                               authSource=MongodbConfig.MONGODB_DB,
                                               authMechanism=MongodbConfig.AUTH_MECHANISM)
            MongodbUtil.__db = MongodbUtil.__client[MongodbConfig.MONGODB_DB]


    @staticmethod
    def _get_connection_dev():
        """
        获取mongodb连接信息
        :return:
        """
        if MongodbUtil.__db is None:
            # 开启连接保活功能（防止长时间不使用连接导致断开）
            MongodbUtil.__client = MongoClient(host=MongodbConfig.MONGODB_HOST, port=MongodbConfig.MONGODB_PORT,
                                               username=MongodbConfig.MONGODB_USER, password=MongodbConfig.MONGODB_PASS,
                                               authSource=MongodbConfig.MONGODB_DB_dev,
                                               authMechanism=MongodbConfig.AUTH_MECHANISM)
            MongodbUtil.__db = MongodbUtil.__client[MongodbConfig.MONGODB_DB_dev]

    @staticmethod
    def coll(collection_name):
        """
        获取指定集合的数据索引
        :param collection_name:集合名称
        :return:
        """
        return MongodbUtil.__db[collection_name]

    @staticmethod
    def insert_one(collection_name, doc_content):
        """
        插入单条数据
        :param collection_name: 集合名称
        :param doc_content: 文档内容
        :return:
        """
        return MongodbUtil.coll(collection_name).insert_one(doc_content)

    @staticmethod
    def insert_many(collection_name, doc_content_list):
        """
        插入多条数据
        :param collection_name: 集合名称
        :param doc_content_list: 文档内容列表
        :return:
        """
        MongodbUtil.coll(collection_name).insert_many(doc_content_list)

    @staticmethod
    def update_one(collection_name, query_filter, update_operation):
        """
        更新单条数据
        :param collection_name: 集合名称
        :param query_filter: 筛选条件
        :param update_operation: 更新内容
        :return:
        """
        MongodbUtil.coll(collection_name).update_one(query_filter, update_operation)

    @staticmethod
    def update_many(collection_name, query_filter, update_operation):
        """
        更新多条数据
        :param collection_name: 集合名称
        :param query_filter: 筛选条件
        :param update_operation: 更新内容
        :return:
        """
        MongodbUtil.coll(collection_name).update_many(query_filter, update_operation)

    @staticmethod
    def del_doc_by_id(collection_name, doc_id):
        """
        根据文档id删除文档
        :param collection_name: 集合名称
        :param doc_id: 文档id
        :return:
        """
        res = MongodbUtil.coll(collection_name).delete_one({'_id': doc_id})
        return res.deleted_count

    @staticmethod
    def del_docs_by_condition(collection_name, del_condition=None):
        """
        功能说明： 使用字典的格式，进行条件删除
        :param collection_name: 数据表名称
        :param del_condition: 删除条件，只能是dict类型，key大于等于一个即可，也可为空
                        可使用修饰符删除：{"name": {"$gt": "H"}}  #读取 name 字段中第一个字母 ASCII 值大于 "H" 的数据
                        使用正则表达式删除：{"$regex": "^R"}    #读取 name 字段中第一个字母为 "R" 的数据
        :return:
        """
        res = MongodbUtil.coll(collection_name).delete_many(del_condition)
        return res.deleted_count

    @staticmethod
    def get_collection_names():
        """
        获取数据库的所有集合名称
        :return:
        """
        return MongodbUtil.__db.collection_names()

    @staticmethod
    def query_all_doc(collection_name):
        """
        查询某个集合的所有文档
        :param collection_name: 集合名称
        :return:
        """
        return MongodbUtil.coll(collection_name).find({})

    @staticmethod
    def query_doc_by_id(collection_name, doc_id):
        """
        基于mongodb查询数据
        :param collection_name: 集合名称
        :param doc_id: 文档id
        :return:
        """
        return MongodbUtil.coll(collection_name).find_one({'_id': doc_id})

    @staticmethod
    def query_docs_by_condition(collection_name, search_condition=None, projection=None):
        """
        功能说明： 使用字典的格式，进行条件查询获取数据
        :param collection_name: 数据表名称
        :param search_condition: 只能是dict类型，key大于等于一个即可，也可为空
                        可使用修饰符查询：{"name": {"$gt": "H"}}  #读取 name 字段中第一个字母 ASCII 值大于 "H" 的数据
                        使用正则表达式查询：{"$regex": "^R"}    #读取 name 字段中第一个字母为 "R" 的数据
        :return:
        """
        # 只会返回数据的索引，数据需要自己取
        result = MongodbUtil.coll(collection_name).find(search_condition, projection)
        return result

    @staticmethod
    def query_one_doc_by_condition(collection_name, search_condition=None):
        """
        功能说明： 使用字典的格式，进行条件查询获取数据，只查询一条
        :param collection_name: 数据表名称
        :param search_condition: 只能是dict类型，key大于等于一个即可，也可为空
                        可使用修饰符查询：{"name": {"$gt": "H"}}  #读取 name 字段中第一个字母 ASCII 值大于 "H" 的数据
                        使用正则表达式查询：{"$regex": "^R"}    #读取 name 字段中第一个字母为 "R" 的数据
        :return:
        """
        result = MongodbUtil.coll(collection_name).find_one(search_condition)
        return result

    @staticmethod
    def update_docs_by_condition(collection_name, search_condition=None, replace_data=None):
        """
        功能说明： 根据search_condition，更新符合条件文档中的某些数据
        :param collection_name: 数据表名称
        :param search_condition: 只能是dict类型，key大于等于一个即可，也可为空
                        可使用修饰符查询：{"name": {"$gt": "H"}}  #读取 name 字段中第一个字母 ASCII 值大于 "H" 的数据
                        使用正则表达式查询：{"$regex": "^R"}    #读取 name 字段中第一个字母为 "R" 的数据
        :param replace_data：用来更新的数据 例{"$set": {"is_remove":1}}
        :return:
        """
        return MongodbUtil.coll(collection_name).update_one(search_condition, replace_data)

    @staticmethod
    def replace_docs_by_condition(collection_name, search_condition=None, replace_data=None):
        """
        功能说明： 根据search_condition，直接用replace_data取代符合条件的数据
        :param collection_name: 数据表名称
        :param search_condition: 只能是dict类型，key大于等于一个即可，也可为空
                        可使用修饰符查询：{"name": {"$gt": "H"}}  #读取 name 字段中第一个字母 ASCII 值大于 "H" 的数据
                        使用正则表达式查询：{"$regex": "^R"}    #读取 name 字段中第一个字母为 "R" 的数据
        :param replace_data：用来更新的数据
        :return:
        """
        return MongodbUtil.coll(collection_name).replace_one(search_condition, replace_data)

    @staticmethod
    def count_docs_by_condition(collection_name, search_condition=None):
        """
        """
        return MongodbUtil.coll(collection_name).count_documents(search_condition)

    @staticmethod
    def list_database_names():
        """
        check
        """
        return MongodbUtil.list_database_names()


if __name__ == '__main__':
    MongodbUtil.connect_dev()
    print(MongodbUtil.coll({'_id': 1}).count())
    # MongodbUtil.get_collection_names()
    #
    doc_list = MongodbUtil.query_all_doc("research_label_info")
    for i, item in enumerate(doc_list):
        if i:
            print(item)