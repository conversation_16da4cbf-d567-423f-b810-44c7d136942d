#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time    : 2024/10/21 15:29
# <AUTHOR> <PERSON><PERSON><PERSON>
# @Email   : <EMAIL>
# @File    : minio_util.py
# @Project : tiance-industry-finance
"""

from typing import Optional
from minio import Minio
from configs.minio_config import MinioConfig


class MinIoUtil(object):
    """
    MinIo工具类
    """

    # 客户端
    min_io_client = None

    @staticmethod
    def connect():
        """
        连接
        :return:
        """
        MinIoUtil.min_io_client = Minio(endpoint=MinioConfig.END_POINT,
                                        access_key=MinioConfig.ACCESS_KEY,
                                        secret_key=MinioConfig.SECRET_KEY,
                                        secure=False)

    @staticmethod
    def reconnect():
        """
        重新连接MinIo
        :return:
        """
        MinIoUtil.min_io_client = None
        MinIoUtil.connect()

    @staticmethod
    def create_bucket(bucket_name):
        """
        创建存储桶
        :param bucket_name: 存储桶名称
        :return:
        """
        # 存储桶是否存在
        if not MinIoUtil.min_io_client.bucket_exists(bucket_name=bucket_name):
            MinIoUtil.min_io_client.make_bucket(bucket_name=bucket_name)

    @staticmethod
    def remove_bucket(bucket_name):
        """
        删除存储桶
        :param bucket_name: 存储桶名称
        :return:
        """
        MinIoUtil.min_io_client.remove_bucket(bucket_name=bucket_name)

    @staticmethod
    def upload_file(bucket_name, remote_path, local_path):
        """
        上传文件
        :param bucket_name: 存储桶名称
        :param remote_path: 远程文件路径
        :param local_path: 本地文件路径
        :return:
        """
        MinIoUtil.min_io_client.fput_object(bucket_name=bucket_name, object_name=remote_path,
                                            file_path=local_path)

    @staticmethod
    def download_file(bucket_name, remote_path, local_path):
        MinIoUtil.min_io_client.fget_object(bucket_name=bucket_name, object_name=remote_path,
                                            file_path=local_path)

    @staticmethod
    def get_file_list(bucket_name, prefix):
        """
        获取文件列表
        :param bucket_name: 存储桶名称
        :param prefix: 前缀
        :return:
        """
        file_list = list()
        object_list = MinIoUtil.min_io_client.list_objects(bucket_name=bucket_name, prefix=prefix)
        for obj in object_list:
            file_list.append(obj.object_name)
        return file_list

    @staticmethod
    def remove_file(bucket_name, file_name):
        """
        删除文件
        :param bucket_name: 存储桶名称
        :param remote_path: 远程文件路径
        :return:
        """
        remote_path = f"pytest/{file_name}"
        MinIoUtil.min_io_client.remove_object(bucket_name, file_name)

    @staticmethod
    def get_all_object(bucket_name, prefix: Optional[str] = None):
        """
        获取文件列表
        :param bucket_name: 存储桶名称
        :param prefix: 前缀
        :return:
        """
        file_list = []
        object_list = MinIoUtil.min_io_client.list_objects(
            bucket_name=bucket_name, recursive=True
        )
        for obj in object_list:
            file_list.append(obj)
        return file_list


if __name__ == '__main__':
    MinIoUtil.connect()

    # 创建存储桶
    b_n = "tiance-industry-finance"
    # MinIoUtil.create_bucket(b_n)

    # # # 上传文件
    # upload_file_list = [
    #     {
    #         "r_p": "news/2023/10/10/工业和信息化部等四部门关于印发绿色航空制造业发展纲要（2023-2035年）的通知.pdf",
    #         "l_p": "../test_scripts/data/news_pdf/工业和信息化部等四部门关于印发绿色航空制造业发展纲要（2023-2035年）的通知.pdf"
    #     },
    #     {
    #         "r_p": "news/2024/03/27/工业和信息化部 科学技术部 财政部 中国民用航空局关于印发《通用航空装备创新应用实施方案（2024-2030年）》的通知.pdf",
    #         "l_p": "../test_scripts/data/news_pdf/工业和信息化部 科学技术部 财政部 中国民用航空局关于印发《通用航空装备创新应用实施方案（2024-2030年）》的通知.pdf"
    #     },
    #     {
    #         "r_p": "news/2024/05/15/提升规模化、网络化、智能化、规范化水平 推动低空经济健康发展.pdf",
    #         "l_p": "../test_scripts/data/news_pdf/提升规模化、网络化、智能化、规范化水平 推动低空经济健康发展.pdf"
    #     },
    #     {
    #         "r_p": "news/2023/02/24/省政府办公厅印发关于推动战略性新兴产业融合集群发展实施方案的通知.pdf",
    #         "l_p": "../test_scripts/data/news_pdf/省政府办公厅印发关于推动战略性新兴产业融合集群发展实施方案的通知.pdf"
    #     },
    #     {
    #         "r_p": "news/2024/05/16/我市出台一系列促进支持低空经济高质量发展的实施方案和政策措施 到2026年，低空经济产业规模超500亿元.pdf",
    #         "l_p": "../test_scripts/data/news_pdf/我市出台一系列促进支持低空经济高质量发展的实施方案和政策措施 到2026年，低空经济产业规模超500亿元.pdf"
    #     },
    #     {
    #         "r_p": "news/2024/05/10/苏州工业园区低空经济高质量发展行动计划（2024—2026年）.pdf",
    #         "l_p": "../test_scripts/data/news_pdf/苏州工业园区低空经济高质量发展行动计划（2024—2026年）.pdf"
    #     },
    #     {
    #         "r_p": "news/2024/04/17/市政府办公室关于印发无锡市低空经济高质量发展三年行动方案（2024—2026年）的通知.pdf",
    #         "l_p": "../test_scripts/data/news_pdf/市政府办公室关于印发无锡市低空经济高质量发展三年行动方案（2024—2026年）的通知.pdf"
    #     },
    #     {
    #         "r_p": "news/2024/05/26/江苏各地乘势而上抢占低空经济制高点.pdf",
    #         "l_p": "../test_scripts/data/news_pdf/江苏各地乘势而上抢占低空经济制高点.pdf"
    #     },
    #     {
    #         "r_p": "news/2025/04/11/省政府办公厅关于印发江苏省数字经济高质量发展三年行动计划（2025－2027年）的通知.pdf",
    #         "l_p": "../test_scripts/data/news_pdf/省政府办公厅关于印发江苏省数字经济高质量发展三年行动计划（2025－2027年）的通知.pdf"
    #     },
    #     {
    #         "r_p": "news/2024/09/11/福州市人民政府关于印发加快推动低空产业发展行动方案的通知.pdf",
    #         "l_p": "../test_scripts/data/news_pdf/福州市人民政府关于印发加快推动低空产业发展行动方案的通知.pdf"
    #     }
    # ]
    # for upload_file in upload_file_list:
    #     MinIoUtil.upload_file(
    #         bucket_name=b_n,
    #         remote_path=upload_file["r_p"],
    #         local_path=upload_file["l_p"]
    #     )

    # 下载文件
    result = MinIoUtil.get_all_object(
        bucket_name=b_n
    )
    for item in result:
        print(item.object_name)
