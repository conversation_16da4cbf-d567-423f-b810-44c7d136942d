from io import BytesIO

import pandas as pd
from openpyxl import Workbook
from openpyxl.utils.exceptions import IllegalCharacterError
import logging

from pandas._typing import FilePath

logging.basicConfig(
    level=logging.WARNING,
    format="%(asctime)s - %(levelname)s - %(message)s"
)


def create_excel_file(
    filename: str,
    headers: list[str],
    rows_data: list[dict],  # 强制类型注解为字典列表
    sheet_name: str = "Sheet1",
    default_value: str | None = "",
    overwrite: bool = True,
) -> None:
    """
       创建通用Excel文件（仅支持字典格式行数据）

       参数:
           filename: 文件名（含路径）
           headers: 表头列表（字符串，需唯一）
           rows_data: 行数据列表，每行可为:
                      - 字典（键需与表头一致，按内容匹配）
           sheet_name: 工作表名称（默认"Sheet1"）
           default_value: 缺失数据时的填充值（默认空字符串）
           overwrite: 是否覆盖已有文件（默认True）
       """
    # 参数校验
    if not isinstance(headers, list) or not all(isinstance(h, str) for h in headers):
        raise TypeError("headers必须是字符串列表")
    if len(headers) != len(set(headers)):
        raise ValueError("表头存在重复项")
    if not isinstance(rows_data, list):
        raise TypeError("rows_data必须是列表")
    if not all(isinstance(row, dict) for row in rows_data):
        raise TypeError("rows_data中的每行必须为字典格式")

    # 文件存在处理
    if not overwrite:
        try:
            with open(filename, "x"):
                pass
        except FileExistsError:
            raise FileExistsError(f"文件{filename}已存在，overwrite=False时无法覆盖")

    # 初始化工作簿
    wb = Workbook()
    if sheet_name != "Sheet1":
        try:
            del wb["Sheet"]
        except KeyError:
            pass
        ws = wb.create_sheet(title=sheet_name)
    else:
        ws = wb.active
        ws.title = sheet_name

    # 写入表头及格式
    try:
        ws.append(headers)
    except IllegalCharacterError as e:
        wb.close()
        raise ValueError(f"表头包含非法字符: {str(e).split(': ')[-1]}")

    # 写入字典数据,按表头匹配
    for row_idx, row_dict in enumerate(rows_data, 2):
        # print(f"{row_idx}<UNK>{row_dict}")
        try:
            # 按表头顺序提取值，缺失键用default_value填充
            row_values = [row_dict.get(header, default_value) for header in headers]
            ws.append(row_values)
        except Exception as e:
            logging.warning(f"第{row_idx - 1}行写入失败: {str(e)}")
            continue

    # 保存文件
    try:
        wb.save(filename)
    except PermissionError:
        wb.close()
        raise PermissionError(f"无权限保存文件: {filename}")
    except Exception as e:
        wb.close()
        raise IOError(f"保存文件失败: {str(e)}")
    finally:
        wb.close()


def write_formated_excel(
    output: FilePath | BytesIO,
    df: pd.DataFrame,
    sheet_name: str = "Sheet1",
):
    """
    将 pandas 的 DataFrame 写入 Excel, 并美化格式

    使用示例：
    =================================
    output = "./test.xlsx" # output 也可以是 io.BytesIO()
    write_formated_excel(output, df)
    =================================

    :param output: 输出句柄，可以是保存路径，也可以是数据流。美化后的表格内容会被写入到这个output
    :param sheet_name: 要写入的工作表名
    :param df: DataFrame
    :return:
    """

    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, index=False, sheet_name=sheet_name)

        column_settings = [{'header': column} for column in df.columns]
        (max_row, max_col) = df.shape

        worksheet = writer.sheets[sheet_name]
        worksheet.add_table(0, 0, max_row, max_col - 1, {'columns': column_settings, 'banded_rows': False})
        worksheet.autofit()


if __name__ == "__main__":
    # 示例代码
    headers = ["姓名", "年龄", "邮箱", "日期", "身份证号"]
    rows_data = [
        {"姓名": "张三", "年龄": 25, "邮箱": "<EMAIL>", "日期": "2025-05-20", "身份证号": 361212200102030908},
        {"姓名": "李四", "邮箱": "<EMAIL>"},  # 缺失年龄自动填充default_value
        {"姓名": "王五", "年龄": 30, "额外字段": "忽略"}  # 多余键忽略
    ]

    create_excel_file(
        r"E:\pycharmData\test.xlsx",
        headers,
        rows_data,
        default_value="",  # 缺失值填充
        sheet_name="用户信息",  # 工作表名称
    )
