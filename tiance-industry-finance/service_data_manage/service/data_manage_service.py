#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 12:30:00
# <AUTHOR> Assistant
# @File         : data_manage_service.py
# @Description  : 数据管理服务层
"""
import re

from fastapi import HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import desc, text
import io
import aiofiles
from datetime import datetime, date
from typing import List, Dict, Any
from fastapi import UploadFile
from typing import Optional
from sqlalchemy import exists

from entity.mysql_entity import CompanyMain
from utils.sql_util import SQLUtil
from utils.log_util import LogUtil
import pandas as pd
from pandas.core.frame import DataFrame

from utils.uuid_util import UuidUtil
from utils.file_content_construction_excel import write_formated_excel
import uuid
from datetime import datetime
import os
from service_data_manage.entity.data_manage_entity import DocumentParseDictEntity
import tempfile
from utils.snow_util import generate_unique_id
from utils.page_util import PageUtil
import re
import json

from fastapi import HTTPException
from service_data_manage.entity.data_manage_entity import Document_DataType, COLLECTION_MAPPING, DATA_TYPE_LABELS
from utils.mongodb_util import MongodbUtil
from utils.minio_util import MinIoUtil


class CompanyDataStatsService:
    """公司数据统计服务类"""

    @staticmethod
    def get_company_data_statistics():
        """
        获取公司数据统计信息
        :return: 包含总量、上市公司总数、当日更新总数的字典
        """
        try:
            # 连接数据库
            SQLUtil.connect()

            # 1. 查询公司数据总量（status=1的记录）
            total_count = SQLUtil.count_records_with_condition(
                CompanyMain,
                CompanyMain.status == 1
            )

            # 2. 查询上市公司总数（StockAbbr不为空且不为null的记录）
            # 使用 SQL 条件查询，避免内存过滤
            from sqlalchemy import and_, func

            listed_count = SQLUtil.count_records_with_condition(
                CompanyMain,
                and_(
                    CompanyMain.status == 1,
                    CompanyMain.StockAbbr.isnot(None),
                    CompanyMain.StockAbbr != '',
                    CompanyMain.StockAbbr != 'NULL',
                    func.trim(CompanyMain.StockAbbr) != ''
                )
            )

            # 3. 查询当日更新总数（update_time >= 今日0点的记录）
            today = date.today()
            today_start = datetime.combine(today, datetime.min.time())

            today_updated_count = SQLUtil.count_records_with_condition(
                CompanyMain,
                and_(
                    CompanyMain.status == 1,
                    CompanyMain.update_time >= today_start
                )
            )

            result = {
                "total_count": total_count,
                "listed_count": listed_count,
                "today_updated_count": today_updated_count,
                "query_date": today.strftime("%Y-%m-%d")
            }

            LogUtil.info(f"公司数据统计查询成功: {result}")
            return result

        except Exception as e:
            error_msg = f"查询公司数据统计失败: {str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            # 关闭数据库连接
            try:
                SQLUtil.close()
            except:
                pass


class CompanyUpdateService:
    """公司数据修改服务类"""

    @staticmethod
    def update_company_info(company_code: str, chi_name: str, chi_name_abbr: str = None, eng_name: str = None):
        """
        修改公司信息
        :param company_code: 企业编号
        :param chi_name: 中文名称（必填）
        :param chi_name_abbr: 企业别称（可选）
        :param eng_name: 英文全称（可选）
        :return: 修改结果字典
        """
        try:
            # 连接数据库
            SQLUtil.connect()

            # 1. 验证输入参数
            if not company_code or not company_code.strip():
                raise ValueError("企业编号不能为空")

            if not chi_name or not chi_name.strip():
                raise ValueError("中文名称不能为空")

            company_code = company_code.strip()
            chi_name = chi_name.strip()

            # 2. 检查公司是否存在（复用 get_company_info 方法）
            CompanyUpdateService.get_company_info(company_code)

            # 3. 检查新的中文名称是否与其他公司重复（复用 check_chi_name_duplicate 方法）
            if CompanyUpdateService.check_chi_name_duplicate(chi_name, company_code):
                raise ValueError(f"中文名称 '{chi_name}' 已被其他公司使用")

            # 4. 获取现有公司数据（重新查询以获取完整的 ORM 对象）
            existing_companies = SQLUtil.query_by_column(
                CompanyMain,
                "CompanyCode",
                company_code,
                exact_match=True,
                limit=1
            )
            existing_company = existing_companies[0]

            # 4. 获取原有数据
            old_chi_name = existing_company.ChiName
            old_pre_name = existing_company.PreName or ""

            # 5. 处理曾用名逻辑
            new_pre_name = CompanyUpdateService._update_pre_name(old_pre_name, old_chi_name, chi_name)

            # 6. 准备更新数据
            update_data = {
                "ChiName": chi_name,
                "PreName": new_pre_name,
                "update_time": datetime.now()
            }

            # 处理可选字段
            if chi_name_abbr is not None:
                update_data["ChiNameAbbr"] = chi_name_abbr.strip() if chi_name_abbr else None

            if eng_name is not None:
                update_data["EngName"] = eng_name.strip() if eng_name else None

            # 7. 执行更新
            updated_company = SQLUtil.update_by_id(
                CompanyMain,
                company_code,
                update_data
            )

            if not updated_company:
                raise Exception("数据库更新失败")

            result = {
                "company_code": company_code,
                "old_chi_name": old_chi_name,
                "new_chi_name": chi_name,
                "chi_name_abbr": updated_company.ChiNameAbbr,
                "eng_name": updated_company.EngName,
                "pre_name": new_pre_name,
                "update_time": updated_company.update_time.strftime('%Y-%m-%d %H:%M:%S'),
                "message": "公司信息修改成功"
            }

            LogUtil.info(f"公司信息修改成功: {result}")
            return result

        except ValueError as e:
            error_msg = str(e)
            LogUtil.error(f"参数验证失败: {error_msg}")
            raise ValueError(error_msg)
        except Exception as e:
            error_msg = f"修改公司信息失败: {str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            # 关闭数据库连接
            try:
                SQLUtil.close()
            except:
                pass

    @staticmethod
    def _update_pre_name(old_pre_name: str, old_chi_name: str, new_chi_name: str) -> str:
        """
        更新曾用名逻辑
        :param old_pre_name: 原有曾用名
        :param old_chi_name: 原有中文名称
        :param new_chi_name: 新的中文名称
        :return: 更新后的曾用名
        """
        # 如果新名称和旧名称相同，不需要更新曾用名
        if old_chi_name == new_chi_name:
            return old_pre_name

        # 解析现有的曾用名
        pre_names = []
        if old_pre_name:
            pre_names = [name.strip() for name in old_pre_name.split(',') if name.strip()]

        # 添加原有中文名称到曾用名中
        if old_chi_name and old_chi_name not in pre_names:
            pre_names.append(old_chi_name)

        # 去重并保持顺序
        unique_pre_names = []
        for name in pre_names:
            if name not in unique_pre_names and name != new_chi_name:
                unique_pre_names.append(name)

        # 返回逗号分隔的字符串
        return ','.join(unique_pre_names) if unique_pre_names else None

    @staticmethod
    def get_company_info(company_code: str):
        """
        获取公司信息
        :param company_code: 企业编号
        :return: 公司信息字典
        """
        try:
            SQLUtil.connect()

            if not company_code or not company_code.strip():
                raise ValueError("企业编号不能为空")

            companies = SQLUtil.query_by_column(
                CompanyMain,
                "CompanyCode",
                company_code.strip(),
                exact_match=True,
                limit=1
            )

            if not companies or companies[0].status != 1:
                raise ValueError(f"企业编号 {company_code} 不存在或已被删除")

            company = companies[0]

            result = {
                "company_code": company.CompanyCode,
                "chi_name": company.ChiName,
                "chi_name_abbr": company.ChiNameAbbr,
                "pre_name": company.PreName,
                "eng_name": company.EngName,
                "eng_name_abbr": company.EngNameAbbr,
                "stock_abbr": company.StockAbbr,
                "brand_name": company.BrandName,
                "large_model_abbr": company.LargeModelAbbr,
                "other_abbr": company.OtherAbbr,
                "credit_code": company.CreditCode,
                "create_time": company.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                "update_time": company.update_time.strftime('%Y-%m-%d %H:%M:%S'),
                "status": company.status
            }

            return result

        except ValueError as e:
            error_msg = str(e)
            LogUtil.error(f"参数验证失败: {error_msg}")
            raise ValueError(error_msg)
        except Exception as e:
            error_msg = f"查询公司信息失败: {str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            try:
                SQLUtil.close()
            except:
                pass

    @staticmethod
    def check_chi_name_duplicate(chi_name: str, exclude_company_code: str = None):
        """
        检查中文名称是否重复
        :param chi_name: 中文名称
        :param exclude_company_code: 排除的企业编号
        :return: 是否重复
        """
        try:
            SQLUtil.connect()

            if not chi_name or not chi_name.strip():
                return False

            # 查询所有同名的公司
            duplicate_companies = SQLUtil.query_by_column(
                CompanyMain,
                "ChiName",
                chi_name.strip(),
                exact_match=True
            )

            # 过滤条件：状态为1且不是排除的公司
            for company in duplicate_companies:
                if company.status == 1:
                    if not exclude_company_code or company.CompanyCode != exclude_company_code.strip():
                        return True

            return False

        except Exception as e:
            LogUtil.error(f"检查名称重复失败: {str(e)}")
            return False
        finally:
            try:
                SQLUtil.close()
            except:
                pass

    @staticmethod
    def batch_update_companies(companies_data: List[Dict[str, Any]]):
        """
        批量修改公司信息（先检查后更新，任何一个数据不符合就返回错误）
        复用 update_company_info 接口进行实际更新操作
        :param companies_data: 公司修改数据列表
        :return: 批量修改结果字典
        """
        try:
            # 1. 数据预处理和验证（遇到错误立即返回）
            validated_companies = []

            for i, company_data in enumerate(companies_data):
                try:
                    # 基础验证
                    company_code = company_data.get('company_code', '').strip()
                    chi_name = company_data.get('chi_name', '').strip()

                    if not company_code:
                        return {
                            "success": False,
                            "error_index": i,
                            "error_company_code": company_code,
                            "error_message": "企业编号不能为空",
                            "error_type": "validation_error",
                            "total_count": len(companies_data),
                            "processed_count": i
                        }

                    if not chi_name:
                        return {
                            "success": False,
                            "error_index": i,
                            "error_company_code": company_code,
                            "error_message": "中文名称不能为空",
                            "error_type": "validation_error",
                            "total_count": len(companies_data),
                            "processed_count": i
                        }

                    validated_companies.append({
                        "index": i,
                        "company_code": company_code,
                        "chi_name": chi_name,
                        "chi_name_abbr": company_data.get('chi_name_abbr'),
                        "eng_name": company_data.get('eng_name')
                    })

                except Exception as e:
                    return {
                        "success": False,
                        "error_index": i,
                        "error_company_code": company_data.get('company_code', ''),
                        "error_message": f"数据格式错误: {str(e)}",
                        "error_type": "format_error",
                        "total_count": len(companies_data),
                        "processed_count": i
                    }

            # 2. 批量检查阶段 - 复用 update_company_info 的验证逻辑
            # 检查公司存在性和名称重复性
            names_to_check = {}

            for company in validated_companies:
                company_code = company['company_code']
                chi_name = company['chi_name']

                try:
                    # 使用现有方法进行检查，复用验证逻辑

                    # 检查公司是否存在
                    CompanyUpdateService.get_company_info(company_code)

                    # 检查名称重复
                    if CompanyUpdateService.check_chi_name_duplicate(chi_name, company_code):
                        return {
                            "success": False,
                            "error_index": company['index'],
                            "error_company_code": company_code,
                            "error_message": f"中文名称 '{chi_name}' 已被其他公司使用",
                            "error_type": "duplicate_error",
                            "total_count": len(companies_data),
                            "processed_count": company['index']
                        }

                    # 检查批量数据内部的重复
                    if chi_name in names_to_check:
                        return {
                            "success": False,
                            "error_index": company['index'],
                            "error_company_code": company_code,
                            "error_message": f"中文名称 '{chi_name}' 在批量数据中重复（与索引 {names_to_check[chi_name]} 重复）",
                            "error_type": "internal_duplicate_error",
                            "total_count": len(companies_data),
                            "processed_count": company['index'],
                            "duplicate_with_index": names_to_check[chi_name]
                        }
                    else:
                        names_to_check[chi_name] = company['index']

                except ValueError as e:
                    # 捕获验证错误（包括公司不存在等）
                    return {
                        "success": False,
                        "error_index": company['index'],
                        "error_company_code": company_code,
                        "error_message": str(e),
                        "error_type": "validation_error",
                        "total_count": len(companies_data),
                        "processed_count": company['index']
                    }
                except Exception as e:
                    return {
                        "success": False,
                        "error_index": company['index'],
                        "error_company_code": company_code,
                        "error_message": f"检查失败: {str(e)}",
                        "error_type": "check_error",
                        "total_count": len(companies_data),
                        "processed_count": company['index']
                    }

            # 3. 所有检查通过，执行批量更新 - 复用 update_company_info 接口
            updated_results = []

            for company in validated_companies:
                try:
                    # 直接调用 update_company_info 进行更新
                    result = CompanyUpdateService.update_company_info(
                        company_code=company['company_code'],
                        chi_name=company['chi_name'],
                        chi_name_abbr=company['chi_name_abbr'],
                        eng_name=company['eng_name']
                    )

                    updated_results.append({
                        "index": company['index'],
                        "company_code": company['company_code'],
                        "old_chi_name": result.get('old_chi_name'),
                        "new_chi_name": result.get('new_chi_name'),
                        "update_time": result.get('update_time')
                    })

                except Exception as e:
                    # 如果更新过程中出现错误，立即返回
                    return {
                        "success": False,
                        "error_index": company['index'],
                        "error_company_code": company['company_code'],
                        "error_message": f"更新失败: {str(e)}",
                        "error_type": "update_error",
                        "total_count": len(companies_data),
                        "processed_count": company['index'],
                        "updated_count": len(updated_results)
                    }

            # 4. 返回成功结果
            return {
                "success": True,
                "total_count": len(companies_data),
                "updated_count": len(updated_results),
                "updated_companies": updated_results,
                "message": f"批量更新成功，共更新 {len(updated_results)} 家公司"
            }

        except Exception as e:
            error_msg = f"批量修改公司信息失败: {str(e)}"
            LogUtil.error(error_msg)
            return {
                "success": False,
                "error_message": error_msg,
                "error_type": "system_error",
                "total_count": len(companies_data) if companies_data else 0,
                "processed_count": 0,
                "updated_count": 0
            }


class CompanyDataUploadService:

    @staticmethod
    def validate(excel_df: DataFrame) -> bool:
        """
        对新增数据的CreditCode、ChiName字段进行查重
        :param excel_df:
        :return:
        """

        try:
            SQLUtil.connect()

            duplicate_rows_df = DataFrame()
            for i in range(len(excel_df)):
                row = excel_df.iloc[i]
                credit_code = row.get("CreditCode")
                chi_name = row.get("ChiName")

                if credit_code is None or str(credit_code).strip() == "":
                    raise ValueError(f"以下公司的'CreditCode'列不能为空，请检查还有没有类似数据。\n\n{row}")

                if chi_name is None or str(chi_name).strip() == "":
                    raise ValueError(f"以下公司的'ChiName'列不能为空，请检查还有没有类似数据。\n\n{row}")

                # 查数据库，检查CreditCode、ChiName字段数据是否重复
                if SQLUtil.get_session().query(
                        exists().where(
                            (
                                    (CompanyMain.CreditCode == credit_code) |
                                    (CompanyMain.ChiName == chi_name)
                            ) &
                            (CompanyMain.status == 1)
                        )
                ).scalar():
                    duplicate_rows_df = duplicate_rows_df._append(row)

            if len(duplicate_rows_df) > 0:
                raise ValueError(
                    f"以下公司的'CreditCode'或'ChiName'列与数据库中数据重复：\n\n{duplicate_rows_df.to_string()}")

            return True

        except Exception as e:
            LogUtil.error(f"{str(e)}")
            raise Exception(e)
        finally:
            try:
                SQLUtil.close()
            except Exception as e:
                raise Exception(e)

    @staticmethod
    def upload(excel_df: DataFrame) -> bool:
        """
        数据上传至 Mysql
        :param excel_df:
        :return:
        """

        try:
            SQLUtil.connect()

            exists_id = SQLUtil.get_max_id(CompanyMain, "CompanyCode")
            match = re.match(r"([A-Za-z]+)(\d+)", exists_id).groups()
            count = int(match[1]) + 1
            primary_key_model = "C{:09d}"

            data_dict_list = []
            for i in range(len(excel_df)):
                row = excel_df.iloc[i]
                column_name_list = SQLUtil.get_table_columns(CompanyMain)

                data_dict = {
                    "CompanyCode": primary_key_model.format(count)
                }
                count += 1

                for column_name in column_name_list:
                    column_value = row.get(column_name)
                    if column_value is not None:
                        data_dict[column_name] = column_value if pd.notna(column_value) else None

                data_dict_list.append(data_dict)

            SQLUtil.insert_many(CompanyMain, data_dict_list)

            return True

        except Exception as e:
            LogUtil.error(f"{str(e)}")
            raise Exception(e)
        finally:
            try:
                SQLUtil.close()
            except Exception as e:
                raise Exception(e)


class CompanyDataDownloadService:

    @staticmethod
    def remove_deleted_item(
            dict_list,
            status_key="status",
            status_deleted_value=0
    ) -> List[Dict[str, Any]]:
        """

        :param dict_list:
        :param status_key:
        :param status_deleted_value:
        :return:
        """

        new_dict_list = []
        for item in dict_list:
            status_value = item.get(status_key)
            if status_value is not None and status_value == status_deleted_value:
                continue
            new_dict_list.append(item)
        return new_dict_list

    @staticmethod
    def remove_attr(
            dict_list,
            remove_key_list=["create_time", "update_time", "status"]
    ) -> List[Dict[str, Any]]:
        """
        过滤掉指定字段
        :param dict_list:
        :param remove_key_list: 待移除的字段列表
        :return:
        """

        new_dict_list = []
        for item in dict_list:
            new_item = {}
            for key in item.keys():
                if key in remove_key_list:
                    continue
                new_item[key] = item[key]
            new_dict_list.append(new_item)
        return new_dict_list

    @staticmethod
    def filter_dict_list(dict_list) -> List[Dict[str, Any]]:

        dict_list = CompanyDataDownloadService.remove_deleted_item(dict_list)
        dict_list = CompanyDataDownloadService.remove_attr(
            dict_list,
            remove_key_list=["create_time", "update_time", "status",
                             "CompanyCode", "EngNameAbbr", "BrandName", "LargeModelAbbr", "OtherAbbr"]
        )

        return dict_list

    @staticmethod
    def download(company_code_list: List[str]) -> io.BytesIO:
        """
        从 Mysql 下载指定公司编号的数据
        :param company_code_list: 公司编号列表
        :return:
        """

        try:
            SQLUtil.connect()

            res_models = SQLUtil.get_data_by_ids(CompanyMain, company_code_list, sort_order="desc")
            res_dict_list = SQLUtil.models_to_list(res_models)
            res_dict_list = CompanyDataDownloadService.filter_dict_list(res_dict_list)

            df = pd.DataFrame(res_dict_list)

            output = io.BytesIO()
            write_formated_excel(output, df)
            output.seek(0)

            return output

        except Exception as e:
            LogUtil.error(f"{str(e)}")
            raise Exception(e)
        finally:
            try:
                SQLUtil.close()
            except Exception as e:
                raise Exception(e)


class DuplicateCompanyError(Exception):
    """公司数据重复异常"""

    def __init__(self, detail: str):
        self.detail = detail
        super().__init__(detail)


class CompanyAddService:
    """公司数据新增服务类"""

    @staticmethod
    def add_companies(company_data_list: list[dict]) -> dict:
        """
        批量新增公司数据
        """
        try:
            SQLUtil.connect()
            results = []

            for company_data in company_data_list:
                try:
                    # 验证数据有效性
                    result = CompanyAddService.add_single_company(company_data)
                    results.append(result)
                except DuplicateCompanyError as e:
                    results.append({
                        "ChiName": company_data.get("ChiName"),
                        "CreditCode": company_data.get("CreditCode"),
                        "message": str(e),
                        "success": False
                    })
                except ValueError as e:
                    results.append({
                        "ChiName": company_data.get("ChiName"),
                        "CreditCode": company_data.get("CreditCode"),
                        "message": f"参数错误: {str(e)}",
                        "success": False
                    })

            # 计算统计信息
            success_count = sum(1 for r in results if r["success"])
            failed_count = len(results) - success_count

            return {
                "total": len(results),
                "success_count": success_count,
                "failed_count": failed_count,
                "details": results
            }

        finally:
            SQLUtil.close()

    @staticmethod
    def add_single_company(company_data: dict) -> dict:
        """
        新增单个公司数据

        Args:
            company_data: 公司数据对象

        Returns:
            dict: 新增结果信息
        """
        LogUtil.info(f"处理新增公司: {company_data['ChiName']}, 统一社会信用代码: {company_data['CreditCode']}")

        # 查重检查
        CompanyAddService._validate_company_unique(
            company_data["CreditCode"],
            company_data["ChiName"]
        )

        # 准备插入数据
        current_time = datetime.now()
        insert_data = CompanyAddService._prepare_company_data(company_data, current_time)

        # 生成CompanyCode
        # company_code = CompanyAddService._generate_company_code()
        company_code = generate_unique_id('C_', datacenter_id=1,
                                          worker_id=1)
        insert_data["CompanyCode"] = company_code

        # 插入数据库
        SQLUtil.insert_one(CompanyMain, insert_data)

        # 构建结果
        return {
            "CompanyCode": company_code,
            "ChiName": company_data["ChiName"],
            "CreditCode": company_data["CreditCode"],
            "message": "新增成功",
            "success": True
        }

    @staticmethod
    def _validate_company_unique(credit_code: str, chi_name: str) -> None:
        """
        验证公司数据唯一性

        Args:
            credit_code: 统一社会信用代码
            chi_name: 中文名称

        Raises:
            DuplicateCompanyError: 公司数据已存在时抛出
        """
        # 检查CreditCode是否存在
        if SQLUtil.exists(CompanyMain, CreditCode=credit_code):
            raise DuplicateCompanyError(f"统一社会信用代码已存在: {credit_code}")

        # 检查ChiName是否存在
        if SQLUtil.exists(CompanyMain, ChiName=chi_name):
            raise DuplicateCompanyError(f"公司名称已存在: {chi_name}")

    @staticmethod
    def _prepare_company_data(company_data: dict, current_time: datetime) -> dict:
        """
        准备公司数据字典

        Args:
            company_data: 公司数据对象
            current_time: 当前时间

        Returns:
            dict: 准备好的数据库插入数据
        """
        return {
            "ChiName": company_data["ChiName"],
            "ChiNameAbbr": company_data.get("ChiNameAbbr"),
            "PreName": company_data.get("PreName"),
            "EngName": company_data.get("EngName"),
            "EngNameAbbr": company_data.get("EngNameAbbr"),
            "StockAbbr": company_data.get("StockAbbr"),
            "BrandName": company_data.get("BrandName"),
            "LargeModelAbbr": company_data.get("LargeModelAbbr"),
            "OtherAbbr": company_data.get("OtherAbbr"),
            "CreditCode": company_data["CreditCode"],
            "status": 1,
            "create_time": current_time,
            "update_time": current_time
        }

    @staticmethod
    def _generate_company_code() -> str:
        """
        生成公司编码（格式：C+9位数字，如C000000001）
        注意：已弃用

        Returns:
            str: 生成的公司编码
        """
        current_max = SQLUtil.get_max_value(CompanyMain, "CompanyCode", "C")
        if current_max:
            current_num = int(current_max[1:])
            new_num = current_num + 1
        else:
            new_num = 1
        return f"C{new_num:09d}"

    @staticmethod
    def convert_to_dict(data: dict) -> dict:
        """将请求数据转换为统一的字典格式"""
        return {
            "ChiName": data["ChiName"],
            "ChiNameAbbr": data.get("ChiNameAbbr"),
            "PreName": data.get("PreName"),
            "EngName": data.get("EngName"),
            "EngNameAbbr": data.get("EngNameAbbr"),
            "StockAbbr": data.get("StockAbbr"),
            "BrandName": data.get("BrandName"),
            "LargeModelAbbr": data.get("LargeModelAbbr"),
            "OtherAbbr": data.get("OtherAbbr"),
            "CreditCode": data["CreditCode"],
        }


class CompanyDelService:
    """公司数据删除服务类"""

    @staticmethod
    def delete_companies(company_list):
        """
        批量删除公司数据（逻辑删除）

        Args:
            company_list: 包含公司列表的请求对象

        Returns:
            dict: 删除结果统计和详细信息
        """
        try:
            # 连接数据库
            SQLUtil.connect()

            # 获取当前时间
            current_time = datetime.now()
            print("current company_list:", company_list)
            # 处理每个公司的删除请求
            results = []
            for company in company_list:
                try:
                    result = CompanyDelService._delete_single_company(company, current_time)
                    results.append(result)
                except Exception as e:
                    results.append({
                        "CompanyCode": getattr(company, "CompanyCode", None),
                        "message": f"删除失败：{str(e)}",
                        "success": False
                    })

            # 检查是否所有操作都失败
            CompanyDelService._check_all_failed(results)

            return {
                "total": len(results),
                "success_count": sum(1 for r in results if r["success"]),
                "failed_count": sum(1 for r in results if not r["success"]),
                "details": results
            }

        except Exception as e:
            error_msg = f"批量删除公司数据失败: {str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            # 关闭数据库连接
            try:
                SQLUtil.close()
            except:
                pass

    @staticmethod
    def _delete_single_company(company, current_time: datetime) -> dict:
        """
        删除单个公司数据

        Args:
            company: 请求对象（包含 CreditCode 和/或 ChiName）
            current_time: 当前时间戳

        Returns:
            dict: 删除结果信息
        """
        LogUtil.info(f"处理删除公司: CompanyCode: {company.CompanyCode}")
        conditions = {"status": 1}

        if company.CompanyCode:
            conditions["CompanyCode"] = company.CompanyCode
        elif company.ChiName:
            conditions["ChiName"] = company.ChiName
        else:
            raise ValueError("无法确定删除条件（缺少 CompanyCode / ChiName）")

        # 验证是否存在
        exists = SQLUtil.exists(CompanyMain, **conditions)
        if not exists:
            LogUtil.warn(f"公司不存在，条件: {conditions}")
            return {
                "CompanyCode": company.CompanyCode,
                "message": "删除失败，公司不存在",
                "success": False
            }

        # 逻辑删除
        update_data = {
            "status": 0,
            "update_time": current_time
        }
        updated_rows = SQLUtil.update(CompanyMain, conditions, update_data)

        if updated_rows > 0:
            LogUtil.info(f"公司删除成功：{conditions}")
            return {
                "CompanyCode": company.CompanyCode,
                "message": "删除成功",
                "success": True
            }
        else:
            LogUtil.warn(f"删除失败：条件匹配但更新无效（{conditions}）")
            return {
                "CompanyCode": company.CompanyCode,
                "message": "删除失败，未找到匹配记录",
                "success": False
            }

    @staticmethod
    def _check_all_failed(results: list):
        """检查是否所有操作都失败，如果是则抛出异常"""
        all_failed = all(not item["success"] for item in results)
        if all_failed:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="所有删除操作均失败，未找到匹配的公司记录"
            )


## 公司数据查询服务类
class CompanySearchService(object):

    ## 按照社会统一信用代码查询
    @staticmethod
    def query_company_by_creditcode(db: Session, creditcode: str, page_num: int,page_size: int):
        res = db.query(CompanyMain).filter(CompanyMain.CreditCode == creditcode,CompanyMain.status == 1).offset((page_num - 1) * page_size).limit(page_size).all()
        count = db.query(CompanyMain).filter(CompanyMain.CreditCode == creditcode,CompanyMain.status == 1).count()
        return res,count
    
    ## 查询所有公司
    @staticmethod
    def query_all_company(db: Session, page_num: int,page_size: int):
        res = db.query(CompanyMain).filter(CompanyMain.status == 1).order_by(desc(CompanyMain.CompanyCode)).offset((page_num - 1) * page_size).limit(page_size).all()
        count = SQLUtil.count_records_with_condition(CompanyMain,CompanyMain.status == 1)
        return res,count
    
    ## 按照公司名称查询
    @staticmethod
    def query_company_by_name(db: Session, company_name:str, page_num: int,page_size: int):
        search = company_name
        res = db.query(CompanyMain).filter(
            CompanyMain.status == 1,
        text("MATCH(CompanyCode, ChiName, ChiNameAbbr, PreName, EngName,StockAbbr) AGAINST(:search_term IN BOOLEAN MODE)")
            ).params(search_term=search)\
        .order_by(desc(CompanyMain.CompanyCode)).offset((page_num - 1) * page_size).limit(page_size).all()
        count = db.query(CompanyMain).filter(
            CompanyMain.status == 1,
            text("MATCH(CompanyCode, ChiName, ChiNameAbbr, PreName, EngName,StockAbbr) AGAINST(:search_term IN BOOLEAN MODE)")
            ).params(search_term=search)\
        .order_by(desc(CompanyMain.CompanyCode)).count()
        return res,count


class DocumentParserService():
    @staticmethod
    def generate_file_metadata(filename: str, dataset_type: str,
                               minio_document_path: str) -> dict:
        """生成文件元数据模板"""
        mongodb_id = uuid.uuid4().hex
        _, file_ext = os.path.splitext(filename)

        return {
            "_id": mongodb_id,
            "data": {
                "data_type": dataset_type,
                "data_source": "手动上传"
            },
            "file": {
                "file_title": filename,
                "file_type": file_ext.lower().lstrip("."),
                "file_url": None,
                "file_flag": {}
            },
            "time": {
                "release_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "crawling_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "parse_time": None
            },
            "minio": {
                "minio_name": "tiance-industry-finance",
                "minio_document_path": [minio_document_path]
            },
            "milvus": {
                "milvus_db_name": None,
                "milvus_collection_name": None
            },
            "status": 1
        }

    @staticmethod
    def upload_single_file_to_minio(
            local_file_path: str,
            bucket_name: str,
            type: str,
            date_path: Optional[str] = None,
    ) -> str:
        """
        上传单个文件到MinIO，按invoice/年/月/日/文件名结构存储

        :param local_file_path: 本地文件完整路径
        :param bucket_name: MinIO存储桶名称
        :param date_path: 可选的自定义日期路径（格式：YYYY/MM/DD）
        :param metadata: 可选的文件元数据
        :return: 文件在MinIO中的存储路径
        """
        # 验证文件存在
        if not os.path.isfile(local_file_path):
            raise FileNotFoundError(f"文件不存在: {local_file_path}")

        # 获取文件名
        filename = os.path.basename(local_file_path)

        # 获取或生成日期路径  默认不给data_path，按照目前的日期来入库
        if date_path is None:
            now = datetime.now()
            date_path = f"{now.year}/{now.month}/{now.day}"
        # 构建MinIO存储路径  类型-日期-文件名
        minio_path = f"/{type}/{date_path}/{filename}".replace("\\", "/")

        # 上传文件
        MinIoUtil.upload_file(
            bucket_name=bucket_name,
            remote_path=minio_path,
            local_path=local_file_path,
        )
        return minio_path

    @staticmethod
    async def process_single_file(file_obj: UploadFile, dataset_type: str) -> dict:
        """处理单个文件上传（使用原始文件名保存）"""
        # 创建本地文件路径
        file_name = file_obj.filename
        local_path = os.path.join(DocumentParseDictEntity.FILE_CACHE_FOLDER, file_name)

        # 确保目录存在
        os.makedirs(os.path.dirname(local_path), exist_ok=True)

        try:
            # 安全写入文件
            async with aiofiles.open(local_path, "wb") as file:
                content = await file_obj.read()
                await file.write(content)

            # 1. MinIO上传
            MinIoUtil.connect()
            minio_category = DocumentParseDictEntity.MINIO_TYPE_DICT.get(dataset_type)
            minio_document_path = DocumentParserService.upload_single_file_to_minio(
                local_path,
                "tiance-industry-finance",
                minio_category
            )

            # 2. 生成元数据
            file_meta = DocumentParserService.generate_file_metadata(
                file_name,
                dataset_type,
                minio_document_path
            )

            # 3. MongoDB存储
            collection_name = DocumentParseDictEntity.MONGO_DATASET_TYPE_DICT[dataset_type]
            MongodbUtil.insert_one(collection_name, file_meta)

            return file_meta

        finally:
            # 清理临时文件
            if os.path.exists(local_path):
                try:
                    os.remove(local_path)
                    LogUtil.debug(f"临时文件已删除: {local_path}")
                except Exception as e:
                    LogUtil.warn(f"临时文件删除失败: {local_path}, 原因: {str(e)}")

    @staticmethod
    async def process_single_subfile(file_obj: UploadFile, dataset_type: str) -> str:
        """处理单个附件文件上传，返回MinIO路径"""
        # 创建本地文件路径
        file_name = file_obj.filename
        local_path = os.path.join(DocumentParseDictEntity.FILE_CACHE_FOLDER, file_name)

        # 确保目录存在
        os.makedirs(os.path.dirname(local_path), exist_ok=True)

        try:
            # 安全写入文件
            async with aiofiles.open(local_path, "wb") as file:
                content = await file_obj.read()
                await file.write(content)

            # MinIO上传
            MinIoUtil.connect()
            minio_category = DocumentParseDictEntity.MINIO_TYPE_DICT.get(dataset_type)
            minio_path = DocumentParserService.upload_single_file_to_minio(
                local_path,
                "tiance-industry-finance",
                minio_category
            )

            return minio_path

        finally:
            # 清理临时文件
            if os.path.exists(local_path):
                try:
                    os.remove(local_path)
                    LogUtil.debug(f"临时文件已删除: {local_path}")
                except Exception as e:
                    LogUtil.warn(f"临时文件删除失败: {local_path}, 原因: {str(e)}")


class QueryDocumentService:
    """mongo数据查询服务类"""

    @staticmethod
    def query_documents(collection_type: str, file_title: str, page: int = 1):
        """
        查询指定集合中匹配标题的文档（分页）

        :param collection_type: 数据集类型 (news/policy/notice/customs/invoice/research)
        :param file_title: 文件标题（模糊匹配）
        :param page: 页码（从1开始）
        :return: 分页结果
        """
        # 映射数据集类型到集合名称
        collection_mapping = {
            "资讯": "news_label_info_new",
            "政策": "policy_label_info_new",
            "公告": "notice_label_info_new",
            "海关": "customs_label_info_new",
            "发票": "invoice_label_info_new",
            "研报": "research_report_label_info_new"
        }

        # 验证数据集类型
        if collection_type not in collection_mapping:
            return {"error": f"无效的dataset_type: {collection_type}", "valid_types": list(collection_mapping.keys())}


        # 获取集合名称
        collection_name = collection_mapping[collection_type]

        # 构建查询条件：status=1 且文件标题模糊匹配
        query = {
            "status": 1,
            "file.file_title": {"$regex": f".*{re.escape(file_title)}.*", "$options": "i"}
        }

        # 指定返回字段：包含所有要求的字段
        projection = {
            "_id": 1,
            "data": 1,  # 包含整个data对象
            "file": 1,  # 包含整个file对象
            "time": 1,  # 包含整个time对象
            "minio": 1,  # 关键修改：包含整个minio对象
            "milvus": 1,  # 包含整个milvus对象
            "status": 1
        }

        try:

            # 执行查询（最多取1000条，避免性能问题）
            cursor = MongodbUtil.query_docs_by_condition(
                collection_name,
                search_condition=query,
                projection=projection
            )

            # 转换结果格式并限制最大数量
            results = []
            for doc in cursor:
                # 提取所有需要的字段
                result_item = {
                    "_id": str(doc["_id"]),
                    "data_type": doc.get("data", {}).get("data_type"),
                    "data_source": doc.get("data", {}).get("data_source"),
                    "file_title": doc.get("file", {}).get("file_title"),
                    "file_type": doc.get("file", {}).get("file_type"),
                    "file_url": doc.get("file", {}).get("file_url"),
                    "file_flag": doc.get("file", {}).get("file_flag", {}),  # 返回整个字典
                    "release_time": doc.get("time", {}).get("release_time"),
                    "crawling_time": doc.get("time", {}).get("crawling_time"),
                    "parse_time": doc.get("time", {}).get("parse_time"),
                    "minio_name": doc.get("minio", {}).get("minio_name"),
                    "minio_document_path": doc.get("minio", {}).get("minio_document_path", []),
                    "milvus_db_name": doc.get("milvus", {}).get("milvus_db_name"),
                    "milvus_collection_name": doc.get("milvus", {}).get("milvus_collection_name"),
                    "status": doc.get("status")
                }
                results.append(result_item)

            results = list(reversed(results))
            # 分页处理（每页10条）
            paginated = PageUtil.paginate_list(results, page, 10)

            # 在方法内部输出结果
            print("查询成功结果:")
            print(json.dumps(paginated, indent=2, ensure_ascii=False))

            return paginated

        except Exception as e:
            return {"error": f"查询失败: {str(e)}"}


class DocumentUpdateService:
    @staticmethod
    def update_document(collection_type: str, document_id: str, update_fields: dict):
        """更新文档服务"""
        collection_name = COLLECTION_MAPPING.get(collection_type)
        if not collection_name:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的集合类型: {collection_type}"
            )

        # 检查文档是否存在
        if not MongodbUtil.query_doc_by_id(collection_name, document_id):
            raise HTTPException(
                status_code=404,
                detail="文档不存在"
            )

        # 构建更新字典
        update_dict = {}

        # 处理data_type更新  弃用，不需要更新datatype
        # if update_fields.get("data_type") is not None:
        #     if update_fields["data_type"] not in DATA_TYPE_LABELS:
        #         raise HTTPException(
        #             status_code=400,
        #             detail=f"无效的数据类型: {update_fields['data_type']}"
        #         )
        #     update_dict["data.data_type"] = DATA_TYPE_LABELS[update_fields["data_type"]]

        # 处理其他字段更新
        if update_fields.get("file_title") is not None:
            update_dict["file.file_title"] = update_fields["file_title"]

        if update_fields.get("file_type") is not None:
            update_dict["file.file_type"] = update_fields["file_type"]

        if update_fields.get("file_url") is not None:
            update_dict["file.file_url"] = update_fields["file_url"]

        if update_fields.get("file_flag") is not None:
            update_dict["file.file_flag"] = update_fields["file_flag"]

        # 检查是否有有效更新字段
        if not update_dict:
            raise HTTPException(
                status_code=400,
                detail="未提供有效的更新字段"
            )

        # 执行更新操作
        try:
            MongodbUtil.update_one(
                collection_name,
                {"_id": document_id},
                {"$set": update_dict}
            )
        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"文档存在但更新失败: {str(e)}"
            )

        return {
            "message": "更新成功",
            "document_id": document_id,
            "updated_fields": list(update_dict.keys())
        }


class DocumentDelService:
    @staticmethod
    def soft_delete_document(data_type: str, document_id: list):
        """软删除文档服务"""
        file_paths_to_delete = []  # 存储需要删除的Minio文件路径
        collection_name = COLLECTION_MAPPING.get(data_type)
        if not collection_name:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的数据类型: {data_type}"
            )
        for doc_id in document_id:
            document = MongodbUtil.query_doc_by_id(collection_name, doc_id)
            if not document:
                raise HTTPException(
                    status_code=404,
                    detail=f"文档不存在: {doc_id}"
                )

            try:
                # 软删除文档
                MongodbUtil.update_one(
                    collection_name,
                    {"_id": doc_id},
                    {"$set": {"status": 0}}
                )

                # 检查并收集Minio文件路径
                minio_bucket = document.get("minio", {}).get("minio_name")
                minio_paths = document.get("minio", {}).get("minio_document_path", [])
                # minio_paths = ['/research_report/2025/4/9/test.pdf']

                if minio_bucket and isinstance(minio_paths, list):
                    for path in minio_paths:
                        if path:  # 确保路径不为空
                            file_path = f"{minio_bucket}{path}"
                            file_paths_to_delete.append(file_path)

            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail=f"文档存在但更新失败: {str(e)}"
                )

        # 如果有需要删除的Minio文件
        if file_paths_to_delete:
            try:
                DocumentDelService.delete_minio_files(file_paths_to_delete)
            except Exception as e:
                print(f"Minio文件删除失败: {str(e)}")

        return {
            "message": "删除成功",
            "document_id": document_id,
            "data_type": data_type,
            "deleted_minio_files": file_paths_to_delete
        }

    @staticmethod
    def delete_minio_files(file_paths: list):
        """删除Minio中的文件
        :param file_paths: 文件路径列表
        """
        if not file_paths:
            return

        MinIoUtil.connect()
        for file_path in file_paths:
            try:
                # 假设file_path格式为"bucket-name/object-name"
                bucket_name, object_name = file_path.split('/', 1)
                MinIoUtil.remove_file(bucket_name, object_name)
            except Exception as e:
                # 记录错误但继续尝试删除其他文件
                print(f"删除Minio文件失败: {file_path}, 错误: {str(e)}")
                continue


class FileDataStatsService:
    @staticmethod
    def file_data_stats(data_type: str):
        collection_name = COLLECTION_MAPPING.get(data_type)
        if not collection_name:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的数据类型: {data_type}"
            )


        collection_results = MongodbUtil.count_docs_by_condition(collection_name, {"status": 1})
        # 查询满足条件的数量
        milvus_results = MongodbUtil.count_docs_by_condition(collection_name,
            {"status":1,"milvus.milvus_collection_name": {
        "$exists": True,
        "$ne": None,
        "$not": {"$eq": ""}}})
        count = 0
        for item in COLLECTION_MAPPING.values():

            All_results = MongodbUtil.count_docs_by_condition(item,{"status":1})
            count += All_results

        result = {
        "count": count,
        "collection_results_num": collection_results,
        "milvus_results_num": milvus_results}


        return result
