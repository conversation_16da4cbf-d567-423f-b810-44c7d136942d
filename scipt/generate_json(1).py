import json
import os
from datetime import datetime
import re
import hashlib

JSON_OUTPUT_DIRECTORY = './generated_jsons'
INPUT_LINKS_FILE = './extracted_data.txt'

os.makedirs(JSON_OUTPUT_DIRECTORY, exist_ok=True)


def generate_json_for_pdf(title, url, data_type, industry_type, publish_time, location):
    json_filename = os.path.join(JSON_OUTPUT_DIRECTORY, f"{title}.json")

    # Determine the root key and minio path prefix based on data_type

    if data_type in ["政策", "政策资讯", "政策解读", "产业政策"]:
        minio_prefix = "policy"
        mongodb_collection_name = "policy_label_info_new"
        data_type_for_db = data_type
    elif data_type in ["研报"]:
        minio_prefix = "research_report"
        mongodb_collection_name = "research_report_label_info_new"
        data_type_for_db = "research_report"
    else:
        minio_prefix = "news"
        mongodb_collection_name = "news_label_info_new"
        data_type_for_db = data_type

    crawling_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    # Generate a random ID based on title
    if url:
        data_hash = hashlib.md5(url.encode(encoding='UTF-8')).hexdigest()
    else:
        data_hash = hashlib.md5(title.encode(encoding='UTF-8')).hexdigest()

    generated_json = dict()

    generated_json["_id"] = data_hash
    # Create the correct root key and populate its content
    generated_json["data"] = {
        "data_type": data_type_for_db,
        "data_source": "手动上传"
    }
    generated_json["file"] = {
        "file_title": title,
        "file_type": "pdf",
        "file_url": url,
        "file_flag": {
            "产业类型": industry_type,
            "地点": location
        }
    }
    generated_json["milvus"] = {
        "milvus_db_name": None,
        "milvus_collection_name": None
    }
    generated_json["minio"] = {
        "minio_name": "tiance-industry-finance",
        "minio_document_path": [
            f"{minio_prefix}/{2025}/{7}/{3}/{title}.pdf"]
    }
    generated_json["time"] = {
        "release_time": publish_time,
        "crawling_time": crawling_time,
        "parse_time": None
    }
    generated_json["status"] = 1

    res_json = {
        "mongodb_collection_name": mongodb_collection_name,
        "generated_json": generated_json
    }

    with open(json_filename, 'w', encoding='utf-8') as f:
        json.dump(res_json, f, ensure_ascii=False, indent=4)
    print(f"Generated JSON for {title}: {json_filename}")


if __name__ == '__main__':

    # The template_json_data is just a structure, we will build the actual JSON from scratch
    # based on the extracted data and the new requirements.

    with open(INPUT_LINKS_FILE, 'r', encoding='utf-8') as f:
        links_data = f.readlines()

    for line in links_data:
        parts = line.strip().split(',', 5)
        print(parts)
        if len(parts) == 6:
            title, url, data_type, industry_type, publish_time, location = parts
            if url == "None":
                url = None
            if location == "None":
                location = None
            generate_json_for_pdf(title, url, data_type, industry_type, publish_time, location)
        else:
            print(f"Skipping malformed line in extracted_data.txt: {line.strip()}")
